"use client";
import { Skeleton } from "@heroui/react";

export default function Loading() {
  return (
    <>
      <p className="m-3 text-sm text-zinc-500">
        Le <span className="text-sm font-bold text-zinc-300">Monete</span> sono
        nate per favorire lo scambio di valore in modo decentralizzato, fungendo
        da alternativa alle valute tradizionali e spesso usate come riserva di
        valore.
        <br /> <br />
        Le <span className="text-sm font-bold text-zinc-300">Blockchain</span> ,
        invece, forniscono l’infrastruttura di base per gestire e registrare
        transazioni in modo trasparente e immutabile, offrendo anche la
        possibilità di creare smart contract e applicazioni decentralizzate
        (DApp).
      </p>
      <div className="border-1 border-zinc-800 rounded-xl p-0 m-2 relative overflow-hidden min-h-[549px]">
        {/* <h2 className="text-xl font-bold text-center p-2 bg-tableHeaderBg sticky left-0 rounded-t-2xl w-100">
          Top Categorie
        </h2> */}
        <Skeleton className="min-h-[549px]"></Skeleton>
      </div>
    </>
  );
}

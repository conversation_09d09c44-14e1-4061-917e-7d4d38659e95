export interface ICryptoRankCoinsData {
  id: number; // Unique numeric identifier
  key: string; // Unique identifier used for URL
  symbol: string | null; // Project symbol (nullable)
  name: string; // Project name
  type:
    | "coin"
    | "token"
    | "etf"
    | "leveraged-token"
    | "fiat"
    | "undefined"
    | "no-token"; // Type of the asset
  rank: number | null; // Project rank (nullable)
  categoryId: number | null; // Unique numeric identifier of the category (nullable)
  category?: {
    id: number;
    key: string;
    name: string;
    numberOfProjects: number;
  } | null; // Category object (nullable)
  lastUpdated: number | null; // The latest date when the data about the project was updated (nullable)
  totalSupply: string | null; // Total number of issued or mined tokens (nullable)
  maxSupply: string | null; // Total number of tokens that will ever be emitted (nullable)
  circulatingSupply: string | null; // Number of tokens which are available in the market (nullable)
  volume24hBase: string | null; // 24 hours trade volume expressed in the base currency (nullable)
  images: Record<string, any> | null; // Project logo (nullable)
  price: string | null; // Current price (nullable)
  high24h: string | null; // The highest price for the last 24 hours (nullable)
  low24h: string | null; // The lowest price for the last 24 hours (nullable)
  volume24h: string | null; // 24 hours trade volume expressed in USD (nullable)
  marketCap: string | null; // Project market capitalization (nullable)
  fullyDilutedValuation: string | null; // Fully diluted valuation (nullable)
  ath: Record<string, any> | null; // All Time High price (nullable)
  atl: Record<string, any> | null; // All Time Low price (nullable)
  sparkline7d: string | null; // URL to the 7-day price graph (nullable)
  percentChange?: Record<string, any>; // 24 hours USD price change (optional)
}

export interface ICryptorankCategories {
  id: number;
  key: string;
  name: string;
  numberOfProjects: number;
  coins?: {
    name: string;
    symbol: string;
    images: Record<string, any>;
  }[];
}

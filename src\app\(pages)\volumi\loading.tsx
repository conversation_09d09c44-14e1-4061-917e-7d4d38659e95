"use client";
import { Skeleton } from "@heroui/react";

export default function Loading() {
  return (
    <div className="border-1 border-zinc-800 rounded-xl p-0 m-2 relative overflow-hidden tablet:[margin-right:-20px] min-h-[402px]">
      <p className="m-3 text-sm text-zinc-500 ">
        I volumi rappresentano un indicatore chiave dell’interesse degli
        investitori, della liquidità di un token o di una moneta e possono
        influenzare la
        <span className="text-sm font-bold text-zinc-300"> volatilità</span> e i
        movimenti di prezzo di un progetto.
      </p>
      <Skeleton className="min-h-[600px]"></Skeleton>
    </div>
  );
}

import { IBtcEtfHistory, IEthEtfHistory } from "@/app/interfaces/googleSheet";
import { formatMarketCap } from "@/app/utils/format-numbers/formatMarketCap";
import {
  <PERSON><PERSON>,
  Drawer,
  <PERSON>er<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ooter,
  Drawer<PERSON>eader,
} from "@heroui/react";
import Image from "next/image";
import { useState } from "react";
import {
  Area,
  AreaChart,
  CartesianGrid,
  ReferenceDot,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

export default function DrawerEtf({
  selectedData,
  data,
  isDrawerOpen,
  onClose,
}: {
  selectedData: "btc" | "eth";
  data: {
    btcEtfHistory: IBtcEtfHistory[];
    ethEtfHistory: IEthEtfHistory[];
  };
  isDrawerOpen: boolean;
  onClose: () => void;
}): React.ReactNode {
  return (
    <>
      <Drawer
        isOpen={isDrawerOpen}
        onOpenChange={onClose}
        placement="bottom"
        size="4xl"
      >
        <DrawerContent>
          {(onClose) => (
            <>
              <DrawerHeader className="flex justify-center">
                <div className="w-100  flex flex-row justify-center items-center">
                  <span className="text-md">
                    {selectedData === "btc" ? "Bitcoin" : "Ethereum"} ETF
                  </span>
                  <Image
                    src={`/${selectedData}.png`}
                    alt={selectedData.toUpperCase()}
                    className="ml-2"
                    width={20}
                    height={20}
                  ></Image>
                </div>
              </DrawerHeader>
              <DrawerBody className="h-[500px] w-100 p-2">
                <EtfChart data={data} selectedData={selectedData}></EtfChart>
              </DrawerBody>
              <DrawerFooter>
                {/* <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
                <Button color="primary" onPress={onClose}>
                  Action
                </Button> */}
              </DrawerFooter>
            </>
          )}
        </DrawerContent>
      </Drawer>
    </>
  );
}

const EtfChart = ({
  selectedData,
  data,
}: {
  selectedData: "btc" | "eth";
  data: { btcEtfHistory: IBtcEtfHistory[]; ethEtfHistory: IEthEtfHistory[] };
}): React.ReactNode => {
  const [timeRange, setTimeRange] = useState<number>(6); // Intervallo in mesi

  // Funzione per calcolare la data di inizio in base all'intervallo selezionato
  const calculateStartDate = (monthsAgo: number): Date => {
    const now = new Date();
    return new Date(now.setMonth(now.getMonth() - monthsAgo));
  };

  const _dataSeries =
    selectedData === "btc" ? data.btcEtfHistory : data.ethEtfHistory;

  const dataSeries = _dataSeries.map((item) => ({
    ...item,
    total$: item.total$ * 1000000,
    daily$: item.daily$ * 1000000,
    date: new Date(item.date).toISOString().split("T")[0], // Converti a YYYY-MM-DD
  }));

  const filteredData = dataSeries.filter((item) => {
    if (timeRange === 0) return item;
    const itemDate = new Date(item.date);
    return itemDate >= calculateStartDate(timeRange);
  });

  const lastDataPoint = filteredData[filteredData.length - 1];

  const btcButtons = [
    { label: "Tutto", value: 0 },
    { label: "1 Anno", value: 12 },
    { label: "6 Mesi", value: 6 },
    { label: "3 Mesi", value: 3 },
  ];

  const ethButtons = [
    { label: "Tutto", value: 0 },
    { label: "6 Mesi", value: 6 },
    { label: "3 Mesi", value: 3 },
  ];

  const buttons = selectedData === "btc" ? btcButtons : ethButtons;

  return (
    <div className="h-[500px]">
      {/* Pulsanti per selezionare l'intervallo temporale */}
      <div className="flex gap-2 mb-3">
        {buttons.map((button) => (
          <Button
            key={button.value}
            size="sm"
            color={timeRange === button.value ? "primary" : "default"}
            onPress={() => setTimeRange(button.value)}
          >
            {button.label}
          </Button>
        ))}
      </div>
      <div className="h-[460px] w-100">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={filteredData}
            height={460}
            margin={{
              top: 10,
              right: -10,
              left: 15,
              bottom: 0,
            }}
            style={{
              backgroundColor: "#1b1b1b",
              borderRadius: "2px",
              padding: "0.5rem",
              border: "1px solid #363636",
            }} // Sfondo scuro
          >
            {/* SOLO LINEE ORIZZONTALI */}
            <CartesianGrid
              stroke="#44475a"
              strokeDasharray="0"
              strokeWidth={0.5}
            />

            {/* ASSE X: Testo bianco con etichette inclinate */}
            <XAxis
              dataKey="date"
              tick={{ fontSize: 12 }}
              tickFormatter={(date) => {
                const d = new Date(date);
                const month = d.toLocaleDateString("it-IT", { month: "short" }); // Mese in italiano
                const year = d.getFullYear();
                return month === "gen" ? `${year}` : month; // Mostra anno su gennaio
              }}
              interval={30} // Mostra tutti i tick
              axisLine={false} // Rimuove la linea dell'asse
              tickLine={false} // Rimuove le linee dei tick
              domain={["dataMin", "dataMax"]}
            />

            {/* ASSE Y: Formattato con percentuali */}
            <YAxis
              tick={{ fontSize: 12, fill: "#ffffff" }}
              tickFormatter={formatMarketCap}
              orientation="right"
              axisLine={false} // Rimuove la linea dell'asse
              tickLine={false} // Rimuove le linee dei tick
              dx={5} // Sposta i label verso destra
              domain={["dataMin", "dataMax"]}
            />

            {/* Tooltip personalizzato */}
            <Tooltip
              wrapperStyle={{ zIndex: 10 }}
              contentStyle={{
                backgroundColor: "#131722",
                color: "#ffffff",
                border: "none",
                padding: "5px",
              }}
              labelStyle={{ color: "#4fc3f7" }}
              formatter={formatMarketCap}
            />
            {/* AREA: Linea blu acceso, sfumatura sotto */}
            <Area
              type="monotone"
              dataKey="total$"
              stroke="#2979ff" // Blu acceso
              strokeWidth={2}
              fill="url(#gradient)" // Usa una sfumatura
              isAnimationActive={false}
            />

            {/* Ultimo punto evidenziato */}
            <ReferenceDot
              x={lastDataPoint.date}
              y={lastDataPoint.total$}
              r={5}
              fill="#2979ff"
              stroke="#ffffff"
              strokeWidth={2}
            />

            {/* Cerchio attorno all'ultimo punto */}
            <ReferenceDot
              x={lastDataPoint.date}
              y={lastDataPoint.total$}
              r={12} // <-- Questo valore crea un cerchio più grande
              fill="none"
              stroke="none"
              strokeOpacity={0.5} // <-- Questo rende il cerchio trasparente
              strokeWidth={2}
            />

            {/* Definizione della sfumatura */}
            <defs>
              <linearGradient id="gradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#2979ff" stopOpacity={0.4} />
                <stop offset="100%" stopColor="#2979ff" stopOpacity={0} />
              </linearGradient>
            </defs>
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

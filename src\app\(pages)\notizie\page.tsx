import { ICriptovalutaNews } from "@/app/interfaces/criptovalutaNews";
import { fetchCriptovalutaNews } from "@/app/services/http/criptovalutaNewsService";
import { NewsList } from "./NewsList";

export default async function Notizie() {
  const _criptovalutaNews = await fetchCriptovalutaNews();

  const filteredCriptovalutaNews = _criptovalutaNews.filter(
    (item: ICriptovalutaNews) =>
      !item.category.includes("Analisi On Chain") &&
      !item.category.includes("Analisi Crypto") &&
      !item.category.includes("Guide")
  );

  return (
    <>
      <div className=" w-full">
        <NewsList criptovalutaNews={filteredCriptovalutaNews!}></NewsList>
      </div>
    </>
  );
}

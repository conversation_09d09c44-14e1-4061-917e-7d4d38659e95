"use client";

import { ICriptovalutaNews } from "@/app/interfaces/criptovalutaNews";
import { timeFromNow } from "@/app/utils/dates/timeFromNow";
import { Listbox, ListboxItem } from "@heroui/react";

export function NewsList({
  criptovalutaNews,
}: {
  criptovalutaNews: ICriptovalutaNews[];
}): React.ReactNode {
  const ListboxWrapper = ({ children }: { children: React.ReactNode }) => (
    <div className="w-4xl">{children}</div>
  );
  return (
    <ListboxWrapper>
      <Listbox className="w-100">
        {criptovalutaNews.map((item: ICriptovalutaNews, index) => (
          <ListboxItem
            key={index}
            value={index}
            classNames={{
              base: `mb-2 rounded-none ${
                index !== criptovalutaNews.length - 1 &&
                "border-b border-zinc-800"
              }`,
              title: "w-100 text-clip pb-2",
            }}
            onPress={() => window.open(item.link[0], "_blank")}
          >
            <div className=" grid grid-cols-[60px_auto] grid-rows-1">
              <div className="col-[1/2] row-[1/3] self-center justify-center text-end text-sm text-zinc-500 pr-4">
                {timeFromNow(item.pubDate[0])}
              </div>
              <div className="mb-1 col-[2/3] row-[1/2]">
                <div className="w-100 overflow-y-hidden">
                  <span className="text-sky-400 text-sm">
                    {item.category[0]}
                  </span>
                </div>
                <div className="col-[2/3] row-[2/2] text-base/5 text-md">
                  {item.title}
                </div>
              </div>
            </div>
          </ListboxItem>
        ))}
      </Listbox>
    </ListboxWrapper>
  );
}

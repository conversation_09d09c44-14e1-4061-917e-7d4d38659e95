import bcrypt from "bcrypt";
import NextAuth, { Session } from "next-auth";
import { JWT } from "next-auth/jwt";
import CredentialsProvider from "next-auth/providers/credentials";

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Custom Login",
      credentials: {
        code: {
          label: "Codice",
          type: "text",
          placeholder: "Inserisci il tuo codice",
        },
      },
      authorize: async (credentials) => {
        const inputCode = credentials?.code;
        const hashBase64 = process.env.AUTH_CODE_HASH_BASE64;

        if (!hashBase64) {
          throw new Error(
            "AUTH_CODE_HASH_BASE64 non è definita. Controlla il file .env.local"
          );
        }

        const hashedCode = Buffer.from(hashBase64, "base64").toString("utf-8");

        // console.log("inputCode:", inputCode, "hashedcode", hashedCode);

        if (!inputCode || !hashedCode) {
          return null; // Mancano i dati necessari
        }

        const isValid = await bcrypt.compare(inputCode, hashedCode);

        if (isValid) {
          // Genera un refresh token fittizio per questo esempio
          const refreshToken = generateFakeRefreshToken();

          return {
            id: "1",
            name: "Utente",
            accessToken: "initial-access-token",
            refreshToken, // Salva il refresh token
            accessTokenExpires: Date.now() + 15 * 60 * 1000, // Scadenza in 15 minuti
            refreshTokenExpires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 giorni
          };
        }

        return null;
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  jwt: {
    secret: process.env.NEXTAUTH_SECRET,
  },
  pages: {
    signIn: "/login",
  },
  callbacks: {
    async jwt({ token, user }: { token: JWT; user?: any }) {
      if (user) {
        return {
          ...token,
          accessToken: user.accessToken,
          refreshToken: user.refreshToken,
          accessTokenExpires: user.accessTokenExpires,
        };
      }

      // Controlla se il token è valido
      if (Date.now() < token.accessTokenExpires) {
        return token; // Token valido
      }

      // Esegui il refresh del token
      return await refreshAccessToken(token);
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      session.accessToken = token.accessToken;
      session.error = token.error;
      return session;
    },
  },
});

// Funzione per generare un refresh token fittizio
function generateFakeRefreshToken() {
  return "fake-refresh-token-" + Math.random().toString(36).substring(7);
}

// Funzione per aggiornare l'access token
async function refreshAccessToken(token: JWT): Promise<JWT> {
  try {
    console.log("Eseguo il refresh del token...");

    // Simula un nuovo access token
    const newAccessToken =
      "refreshed-access-token-" + Math.random().toString(36).substring(7);

    return {
      ...token,
      accessToken: newAccessToken,
      accessTokenExpires: Date.now() + 15 * 60 * 1000, // 15 minuti
    };
  } catch (error) {
    console.error("Errore durante il refresh del token:", error);

    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}

// Esporta il gestore come named export per i metodi HTTP
export { handler as GET, handler as POST };

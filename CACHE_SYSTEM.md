# Sistema di Cache Manuale

Questo progetto implementa un sistema di cache manuale utilizzando localStorage per simulare il comportamento di ISG (Incremental Static Generation) per le chiamate API.

## Come Funziona

Il sistema di cache salva i risultati delle chiamate API nel localStorage del browser con un timestamp di scadenza. Se i dati sono ancora validi (non scaduti), vengono restituiti dalla cache invece di effettuare una nuova chiamata API.

## Caratteristiche

- ✅ **Cache automatica**: Tutte le chiamate API sono automaticamente cachate
- ✅ **TTL configurabile**: Ogni API può avere un tempo di scadenza diverso
- ✅ **Gestione errori**: Cache corrotta viene automaticamente rimossa
- ✅ **Debug UI**: Interfaccia per monitorare e gestire la cache
- ✅ **SSR Safe**: Funziona sia lato client che server

## Configurazione Cache per API

### CoinGecko
- **Coins Data**: 1 minuto (dati di prezzo che cambiano frequentemente)
- **Categories**: 5 minuti (categorie cambiano raramente)
- **Company Holdings**: 30 minuti (dati aziendali cambiano molto raramente)

### CoinMarketCap
- **Coins Data**: 1 minuto (dati di prezzo che cambiano frequentemente)

### Bitbo Treasuries
- **BTC Global**: 60 minuti (dati treasury cambiano lentamente)

### Criptovaluta News
- **News Feed**: 2 minuti (news cambiano frequentemente ma non ogni secondo)

## Utilizzo

### Per Nuove API

```typescript
import CacheService from '@/app/services/http/cacheService';

export async function fetchMyApiData(): Promise<MyDataType> {
  return CacheService.fetchWithCache(
    {
      key: 'my-api-data',
      ttlMinutes: 5 // Cache per 5 minuti
    },
    async () => {
      // La tua logica di fetch qui
      const response = await fetch('https://api.example.com/data');
      return response.json();
    }
  );
}
```

### Gestione Cache Programmatica

```typescript
import CacheService from '@/app/services/http/cacheService';

// Pulire una cache specifica
CacheService.clearCache('coingecko-coins-data');

// Pulire tutte le cache con un prefisso
CacheService.clearCacheByPrefix('coingecko-');

// Ottenere informazioni su una cache
const info = CacheService.getCacheInfo('coingecko-coins-data');
console.log('Cache exists:', info.exists);
console.log('Expires at:', info.expiresAt);
console.log('Age:', info.age);
```

## Cache Manager UI

Il progetto include un componente `CacheManager` che fornisce un'interfaccia utente per:

- 📊 Visualizzare tutte le cache attive
- ⏰ Vedere quando scadranno
- 📏 Controllare le dimensioni
- 🗑️ Pulire cache specifiche o tutte
- 🔄 Aggiornare la vista in tempo reale

### Come Accedere

1. Apri l'applicazione nel browser
2. Cerca il pulsante "🗂️ Cache Manager" in basso a destra
3. Clicca per aprire il pannello di gestione

## Vantaggi

### Performance
- **Riduzione chiamate API**: Evita chiamate duplicate entro il TTL
- **Caricamento più veloce**: Dati istantanei dalla cache
- **Riduzione costi**: Meno chiamate = meno costi API

### User Experience
- **Navigazione fluida**: Cambio pagina senza attese
- **Offline resilience**: Dati disponibili anche con connessione instabile
- **Consistenza**: Stessi dati mostrati durante il TTL

### Sviluppo
- **Debug facile**: UI per monitorare la cache
- **Configurabile**: TTL diversi per ogni API
- **Trasparente**: Stesso codice, cache automatica

## Considerazioni

### Memoria
- I dati sono salvati nel localStorage (limite ~5-10MB)
- Cache automaticamente pulita quando scade
- Gestione errori per cache corrotta

### Sincronizzazione
- Cache locale per ogni browser/dispositivo
- Non sincronizzata tra dispositivi
- Ideale per dati che non richiedono sincronizzazione real-time

### Sviluppo vs Produzione
- In sviluppo: TTL bassi per test rapidi
- In produzione: TTL ottimizzati per performance

## Monitoraggio

### Console Logs
Il sistema logga automaticamente:
- `📦 Cache HIT` quando i dati vengono presi dalla cache
- `🌐 Cache MISS` quando viene fatta una nuova chiamata API
- `💾 Cache MISS` quando i dati vengono salvati in cache

### Cache Manager
- Visualizzazione real-time delle cache
- Informazioni su scadenza e dimensioni
- Controlli per pulizia selettiva

## Troubleshooting

### Cache Non Funziona
1. Verifica che localStorage sia disponibile
2. Controlla la console per errori
3. Verifica che la chiave cache sia corretta

### Dati Vecchi
1. Controlla il TTL configurato
2. Pulisci manualmente la cache specifica
3. Verifica l'orologio di sistema

### Performance Issues
1. Controlla la dimensione totale della cache
2. Riduci i TTL se necessario
3. Pulisci cache non utilizzate

## Estensioni Future

- [ ] Cache condivisa tra tab (BroadcastChannel)
- [ ] Compressione dati cache
- [ ] Cache persistence oltre localStorage
- [ ] Invalidazione cache intelligente
- [ ] Metriche cache avanzate

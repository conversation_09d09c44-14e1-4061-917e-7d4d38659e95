// Enum per le chiavi cache predefinite
export enum CacheKeys {
  // CoinGecko
  COINGECKO_COINS = "coingecko-coins-data",
  COINGECKO_CATEGORIES = "coingecko-categories-data",
  COINGECKO_COMPANY_HOLDERS = "coingecko-company-holders-data",

  // CoinMarketCap
  COINMARKETCAP_COINS = "coinmarketcap-coins-data",

  // Bitbo Treasuries
  BITBO_TREASURIES = "bitbo-treasuries-data",

  // Criptovaluta News
  CRIPTOVALUTA_NEWS = "criptovaluta-news-data",

  // <PERSON><PERSON> servizi (aggiungi qui nuove chiavi)
  CRYPTORANK_DATA = "cryptorank-data",
  DEFILLAMA_DATA = "defillama-data",
  GEMINI_DATA = "gemini-data",
  RSS_FEED = "rss-feed-data",
}

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface CacheConfig {
  key: CacheKeys | string; // Permette sia enum che stringhe custom
  ttlMinutes?: number; // Time to live in minutes, default 1 minute
}

class CacheService {
  private static readonly DEFAULT_TTL_MINUTES = 1;

  /**
   * Checks if we're in a browser environment
   */
  private static isBrowser(): boolean {
    return typeof window !== "undefined" && typeof localStorage !== "undefined";
  }

  /**
   * Gets cached data if it exists and is not expired
   */
  private static getCachedData<T>(key: string): T | null {
    if (!this.isBrowser()) {
      return null;
    }

    try {
      const cachedItem = localStorage.getItem(key);
      if (!cachedItem) {
        return null;
      }

      const parsed: CacheItem<T> = JSON.parse(cachedItem);
      const now = Date.now();

      // Check if cache is expired
      if (now > parsed.expiresAt) {
        localStorage.removeItem(key);
        return null;
      }

      console.log(`📦 Cache HIT per ${key} - dati recuperati dalla cache`);
      return parsed.data;
    } catch (error) {
      console.error(`Errore nel recupero cache per ${key}:`, error);
      // Remove corrupted cache entry
      localStorage.removeItem(key);
      return null;
    }
  }

  /**
   * Stores data in cache with expiration
   */
  private static setCachedData<T>(
    key: string,
    data: T,
    ttlMinutes: number
  ): void {
    if (!this.isBrowser()) {
      return;
    }

    try {
      const now = Date.now();
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: now,
        expiresAt: now + ttlMinutes * 60 * 1000,
      };

      localStorage.setItem(key, JSON.stringify(cacheItem));
      console.log(
        `💾 Cache MISS per ${key} - dati salvati in cache per ${ttlMinutes} minuti`
      );
    } catch (error) {
      console.error(`Errore nel salvataggio cache per ${key}:`, error);
    }
  }

  /**
   * Main method to fetch data with caching
   * If cached data exists and is not expired, returns cached data
   * Otherwise, executes the fetch function and caches the result
   */
  static async fetchWithCache<T>(
    config: CacheConfig,
    fetchFunction: () => Promise<T>
  ): Promise<T> {
    const { key, ttlMinutes = this.DEFAULT_TTL_MINUTES } = config;

    // Try to get cached data first
    const cachedData = this.getCachedData<T>(key);
    if (cachedData !== null) {
      return cachedData;
    }

    // Cache miss - fetch new data
    console.log(`🌐 Cache MISS per ${key} - effettuando nuova chiamata API...`);
    const freshData = await fetchFunction();

    // Store in cache
    this.setCachedData(key, freshData, ttlMinutes);

    return freshData;
  }

  /**
   * Clears specific cache entry
   */
  static clearCache(key: string): void {
    if (!this.isBrowser()) {
      return;
    }
    localStorage.removeItem(key);
    console.log(`🗑️ Cache cleared per ${key}`);
  }

  /**
   * Clears all cache entries that start with a prefix
   */
  static clearCacheByPrefix(prefix: string): void {
    if (!this.isBrowser()) {
      return;
    }

    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => {
      localStorage.removeItem(key);
    });

    console.log(
      `🗑️ Cache cleared per ${keysToRemove.length} chiavi con prefisso "${prefix}"`
    );
  }

  /**
   * Gets cache info for debugging
   */
  static getCacheInfo(key: string): {
    exists: boolean;
    expiresAt?: Date;
    age?: number;
  } {
    if (!this.isBrowser()) {
      return { exists: false };
    }

    try {
      const cachedItem = localStorage.getItem(key);
      if (!cachedItem) {
        return { exists: false };
      }

      const parsed: CacheItem<any> = JSON.parse(cachedItem);
      return {
        exists: true,
        expiresAt: new Date(parsed.expiresAt),
        age: Date.now() - parsed.timestamp,
      };
    } catch {
      return { exists: false };
    }
  }
}

export default CacheService;

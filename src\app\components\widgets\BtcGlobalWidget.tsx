"use client";
import { IBtcGlobal } from "@/app/interfaces/btc-global";
import { formatMarketCap } from "@/app/utils/format-numbers/formatMarketCap";
import { Card, CardBody } from "@heroui/react";
import { useEffect } from "react";
import { GlobeIcoin } from "../shared/Icons";

export const BtcGlobalWidget = ({
  btcGlobal,
}: {
  btcGlobal: IBtcGlobal;
}): React.ReactNode => {
  // Debug: log quando i dati cambiano
  useEffect(() => {
    console.log("🔄 BtcGlobalWidget - dati aggiornati:", {
      hasBtcGlobal: !!btcGlobal,
      hasSummary: !!btcGlobal?.summary,
      summaryBtcTotal: btcGlobal?.summary?.btcTotal,
      historyLength: btcGlobal?.history?.length,
      dataLength: btcGlobal?.data?.length,
      history: btcGlobal?.history?.map((h, i) => ({
        index: i,
        date: h.date,
        btcTotal: h.btcTotal,
        hasBtcTotal: !!h.btcTotal,
      })),
    });
  }, [btcGlobal]);
  const categoryTranslations: { [key: string]: string } = {
    ETFs: "ETF",
    Countries: "Nazioni",
    "Public Companies": "Aziende Pubbliche",
    "Private Companies": "Aziende Private",
    "BTC Mining Companies": "Aziende di Mining",
    Defi: "DeFi",
  };

  const summary = btcGlobal?.summary;
  const data = btcGlobal?.data
    ?.sort((a, b) => +b.btcHoldings - +a.btcHoldings)
    ?.map((item) => ({
      ...item,
      category: categoryTranslations[item.category] || item.category,
    }));

  const btcGlobalHistory = btcGlobal?.history?.slice();

  btcGlobalHistory?.forEach((item) => {
    item.data.forEach((data) => {
      data.category = categoryTranslations[data.category] || data.category;
    });
  });

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("it-IT").format(num);
  };

  return (
    <Card>
      <CardBody className="p-3">
        {/* <div className="flex justify-between items-center">
            <span className="text-xs text-zinc-400 font-bold">
              Istituzioni in possesso di BTC
            </span>
          </div> */}

        <div className="grid grid-cols-[135px_100px_auto] gap-2 w-100 font-semibold border-b border-zinc-800 pb-1 text-zinc-400 ">
          <div className="text-sm flex text-btcColor">
            {" "}
            <GlobeIcoin className="mr-2" fill="#F7931A"></GlobeIcoin>
            Bitcoin
          </div>
          <div className="text-sm text-end">Tot. BTC</div>
          <div className="text-sm text-end">Tot. $</div>
          {/* <div className="text-end text-sm">% 21M</div> */}
        </div>
        <div className="flex flex-col justify-start w-100  border-b border-zinc-800 mb-1  text-zinc-500 font-semibold">
          {data && data.length > 0 ? (
            data.map((item, index, array) => (
              <div
                key={index}
                className={`${
                  index !== array.length - 1 && ""
                } grid grid-cols-[135px_100px_auto] mt-1 gap-2 pb-1 `}
              >
                <div className=" text-sm">{item.category}</div>
                <div className="text-sm text-end">
                  {item.btcHoldings ? formatNumber(+item.btcHoldings) : "0"}
                </div>
                <div className="text-sm text-end">
                  {item.valueToday
                    ? formatMarketCap(+item.valueToday.slice(1))
                    : "$0"}
                </div>
                {/* <div className="text-end text-sm">
                    {(+item.percentageOf21m.replace("%", ""))
                      .toFixed(1)
                      .replace(".", ",") + " %"}
                  </div> */}
              </div>
            ))
          ) : (
            <div className="text-center py-4 text-gray-500">
              Caricamento dati...
            </div>
          )}
        </div>

        <div className="grid grid-cols-[135px_100px_auto] gap-2 w-100 my-1 pb-2 font-semibold border-b border-zinc-800">
          <div className="text-sm">Totale</div>
          <div className="text-sm text-end">
            <span className="text-btcColor text-sm mr-1">₿</span>
            {summary?.btcTotal ? formatNumber(+summary.btcTotal) : "0"}
          </div>
          <div className="text-sm text-end">
            <span className="text-sm mr-1">$</span>
            {summary?.valueToday
              ? formatMarketCap(+summary.valueToday.slice(1))
              : "$0"}
          </div>
          {/* <div className="text-right text-sm">
              {(+summary?.percentageOf21m.replace("%", "")!)
                .toFixed(1)
                .replace(".", ",") + " %"}
            </div> */}
        </div>

        <div className="grid grid-cols-[135px_100px_auto] gap-2 w-100 mt-1 font-semibold">
          <div className="text-sm text-zinc-500">2025 (in corso)</div>
          <div className="text-sm text-end">
            {/* <span className={"text-btcColor text-sm mr-1"}>₿</span> */}
            {summary?.btcTotal && btcGlobalHistory?.[2] ? (
              <span
                className={`${
                  +summary.btcTotal - (+btcGlobalHistory[2].btcTotal || 0) > 0
                    ? "text-green-500"
                    : "text-red-500"
                } text-sm`}
              >
                {+summary.btcTotal - (+btcGlobalHistory[2].btcTotal || 0) > 0
                  ? "+ " +
                    formatNumber(
                      +summary.btcTotal - (+btcGlobalHistory[2].btcTotal || 0)
                    )
                  : "- " +
                    formatNumber(
                      Math.abs(
                        +summary.btcTotal - (+btcGlobalHistory[2].btcTotal || 0)
                      )
                    )}
              </span>
            ) : (
              <span className="text-gray-500 text-sm">
                {summary ? "Dati storici non disponibili" : "Caricamento..."}
              </span>
            )}

            {/* {formatNumber(+summary?.btcTotal!)} */}
          </div>
          {/* <div className="text-sm text-end">
            <span className="text-sm mr-1">$</span>
            {formatMarketCap(
              +btcGlobalHistory?.[2]?.value - +summary?.valueToday.slice(1)!
            )}
          </div> */}
          {/* <div className="text-right text-sm">
              {(+summary?.percentageOf21m.replace("%", "")!)
                .toFixed(1)
                .replace(".", ",") + " %"}
            </div> */}
        </div>

        <div className="grid grid-cols-[135px_100px_auto] gap-2 w-100 mt-1 font-semibold">
          <div className="text-sm text-zinc-500">2024</div>
          <div className="text-sm text-end">
            {/* <span className={"text-btcColor text-sm mr-1"}>₿</span> */}
            {btcGlobalHistory?.[2] && btcGlobalHistory?.[1] ? (
              <span
                className={`${
                  (+btcGlobalHistory[2].btcTotal || 0) -
                    (+btcGlobalHistory[1].btcTotal || 0) >
                  0
                    ? "text-green-500"
                    : "text-red-500"
                } text-sm`}
              >
                {(+btcGlobalHistory[2].btcTotal || 0) -
                  (+btcGlobalHistory[1].btcTotal || 0) >
                1
                  ? "+ " +
                    formatNumber(
                      (+btcGlobalHistory[2].btcTotal || 0) -
                        (+btcGlobalHistory[1].btcTotal || 0)
                    )
                  : "- " +
                    formatNumber(
                      Math.abs(
                        (+btcGlobalHistory[2].btcTotal || 0) -
                          (+btcGlobalHistory[1].btcTotal || 0)
                      )
                    )}
              </span>
            ) : (
              <span className="text-gray-500 text-sm">
                {btcGlobalHistory
                  ? "Dati storici incompleti"
                  : "Caricamento..."}
              </span>
            )}

            {/* {formatNumber(+summary?.btcTotal!)} */}
          </div>
          {/* <div className="text-sm text-end">
            <span className="text-sm mr-1">$</span>
            {formatMarketCap(
              +btcGlobalHistory?.[2]?.value - +summary?.valueToday.slice(1)!
            )}
          </div> */}
          {/* <div className="text-right text-sm">
              {(+summary?.percentageOf21m.replace("%", "")!)
                .toFixed(1)
                .replace(".", ",") + " %"}
            </div> */}
        </div>
        <div className="grid grid-cols-[135px_100px_auto] gap-2 w-100 mt-1 font-semibold">
          <div className="text-sm text-zinc-500">2023</div>
          <div className="text-sm text-end">
            {/* <span className={"text-btcColor text-sm mr-1"}>₿</span> */}
            {btcGlobalHistory?.[1] && btcGlobalHistory?.[0] ? (
              <span
                className={`${
                  (+btcGlobalHistory[1].btcTotal || 0) -
                    (+btcGlobalHistory[0].btcTotal || 0) >
                  0
                    ? "text-green-500"
                    : "text-red-500"
                } text-sm`}
              >
                {(+btcGlobalHistory[1].btcTotal || 0) -
                  (+btcGlobalHistory[0].btcTotal || 0) >
                1
                  ? "+ " +
                    formatNumber(
                      (+btcGlobalHistory[1].btcTotal || 0) -
                        (+btcGlobalHistory[0].btcTotal || 0)
                    )
                  : "- " +
                    formatNumber(
                      Math.abs(
                        (+btcGlobalHistory[1].btcTotal || 0) -
                          (+btcGlobalHistory[0].btcTotal || 0)
                      )
                    )}
              </span>
            ) : (
              <span className="text-gray-500 text-sm">
                {btcGlobalHistory
                  ? "Dati storici incompleti"
                  : "Caricamento..."}
              </span>
            )}

            {/* {formatNumber(+summary?.btcTotal!)} */}
          </div>
          {/* <div className="text-sm text-end">
            <span className="text-sm mr-1">$</span>
            {formatMarketCap(
              +btcGlobalHistory?.[2]?.value - +summary?.valueToday.slice(1)!
            )}
          </div> */}
          {/* <div className="text-right text-sm">
              {(+summary?.percentageOf21m.replace("%", "")!)
                .toFixed(1)
                .replace(".", ",") + " %"}
            </div> */}
        </div>
      </CardBody>
    </Card>
  );
};

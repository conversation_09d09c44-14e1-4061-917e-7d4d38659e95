import CategoriesList from "@/app/components/tables/CategoriesList";
import { fetchCoinmarketcapCoinsData } from "@/app/services/http/coinmarketcapService";
import { fetchCryptorankCoinsData } from "@/app/services/http/cryptorankService";

export default async function Categorie() {
  const [_cryptorankCoinsData, _coinmarketcapCoinsData] = await Promise.all([
    fetchCryptorankCoinsData(),
    fetchCoinmarketcapCoinsData(),
  ]);
  return (
    <div className="mb-4">
      <p className="m-3 text-sm text-zinc-500">
        Le <span className="text-sm font-bold text-zinc-300">Monete</span> sono
        nate per favorire lo scambio di valore in modo decentralizzato, fungendo
        da alternativa alle valute tradizionali e spesso usate come riserva di
        valore.
        <br /> <br />
        Le <span className="text-sm font-bold text-zinc-300">Blockchain</span> ,
        invece, forniscono l’infrastruttura di base per gestire e registrare
        transazioni in modo trasparente e immutabile, offrendo anche la
        possibilità di creare smart contract e applicazioni decentralizzate
        (DApp).
      </p>
      <CategoriesList
        cryptorankCoinsData={_cryptorankCoinsData}
        coinmarketcapCoinsData={_coinmarketcapCoinsData}
      ></CategoriesList>
    </div>
  );
}

"use client";
import { Skeleton } from "@heroui/react";

export default function Loading() {
  return (
    <>
      <p className="m-3 text-sm text-zinc-500 ">
        I token sono rappresentazioni digitali di{" "}
        <span className="text-sm font-bold text-zinc-300">
          valore o diritti
        </span>{" "}
        emessi su una blockchain. <br /> <br />
        Possono fungere da{" "}
        <span className="text-sm font-bold text-zinc-300">
          monete virtuali, strumenti di governance
        </span>{" "}
        (es. diritto di voto in un progetto), o certificare la{" "}
        <span className="text-sm font-bold text-zinc-300">proprietà</span> di un
        bene.
        <br /> <br />
        Sfruttando la tecnologia blockchain, le transazioni di token sono
        trasparenti, sicure e difficilmente modificabili.
      </p>
      <div className="border-1 border-zinc-800 rounded-xl p-0 m-2 relative overflow-hidden  min-h-[1116px]">
        {/* <h2 className="text-xl font-bold text-center p-2 bg-tableHeaderBg sticky left-0 rounded-t-2xl w-100">
          Top Tokens
        </h2> */}
        <Skeleton className="min-h-[1000px]"></Skeleton>
      </div>
    </>
  );
}

import { NextResponse } from 'next/server';

const GOOGLE_SHEET_ETF_URL =
  "https://sheets.googleapis.com/v4/spreadsheets/1QO-h_7S4vMQEy3LxTvGFt7LaGfCD0NL_-jWsAJ1RF68/values/EtfBtc?alt=json&key=AIzaSyAhXXS5ctqKfELybuqs88pkDBLOWN8FGfs";

export async function GET() {
  try {
    console.log("Effettuando una nuova chiamata a Google Sheet - ETF...");
    
    const response = await fetch(GOOGLE_SHEET_ETF_URL);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData: {
      majorDimensios: string;
      range: string;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      values: any[];
    } = await response.json();

    responseData.values = responseData.values.slice(1);

    const _btcHistory: any[] = [],
      _ethHistory: any[] = [];
    
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    responseData.values.forEach((item: any) => {
      _btcHistory.push({
        date: item[0].split("/").reverse().join("/"),
        daily$: +item[1],
        dailyBtc: +item[2],
        total$: +item[3],
        totalBtc: +item[4],
      });

      if (!!item[6]) {
        _ethHistory.push({
          date: item[6].split("/").reverse().join("/"),
          daily$: +item[7],
          dailyEth: +item[8],
          total$: +item[9],
          totalEth: +item[10],
        });
      }
    });

    const data = {
      btcEtfHistory: _btcHistory,
      ethEtfHistory: _ethHistory,
    };

    return NextResponse.json(data);
  } catch (error) {
    console.error("Errore durante il fetch da Google Sheet (ETF):", error);
    return NextResponse.json(
      { error: "Errore durante il recupero dei dati Google Sheet (ETF)" },
      { status: 500 }
    );
  }
}

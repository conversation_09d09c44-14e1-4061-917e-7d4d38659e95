import {
  IBtcGlobal,
  IBtcGlobalCategoryData,
  IBtcGlobalHistory,
  IBtcGlobalSummaryData,
} from "@/app/interfaces/btc-global";
import * as cheerio from "cheerio";

const BITBO_URL: string = "https://treasuries.bitbo.io/"; // URL della pagina da cui fare scraping

const BTCGLOBAL_HISTORY: IBtcGlobalHistory[] = [
  {
    date: "2023-01-01",
    btcPrice: 16500,
    btcTotal: 1718123,
    value: 28349029500,
    data: [
      {
        category: "Private Companies",
        btcHoldings: 430291,
      },
      {
        category: "Countries",
        btcHoldings: 100240,
      },
      {
        category: "Defi",
        btcHoldings: 186583,
      },
      {
        category: "ETFs",
        btcHoldings: 794875,
      },
      {
        category: "Public Companies",
        btcHoldings: 206134,
      },
    ],
  },
  {
    date: "2024-01-01",
    btcPrice: 42900,
    btcTotal: 1781691,
    value: 76434543900,
    data: [
      {
        category: "Private Companies",
        btcHoldings: 488270,
      },
      {
        category: "Countries",
        btcHoldings: 90379,
      },
      {
        category: "Defi",
        btcHoldings: 159252,
      },
      {
        category: "ETFs",
        btcHoldings: 771013,
      },
      {
        category: "Public Companies",
        btcHoldings: 272777,
      },
    ],
  },
  {
    date: "2025-01-01",
    btcPrice: 94000,
    btcTotal: 2946957,
    value: 277013958000,
    data: [
      {
        category: "Private Companies",
        btcHoldings: 407201,
      },
      {
        category: "Countries",
        btcHoldings: 513792,
      },
      {
        category: "Defi",
        btcHoldings: 145116,
      },
      {
        category: "ETFs",
        btcHoldings: 1289031,
      },
      {
        category: "Public Companies",
        btcHoldings: 591817,
      },
    ],
  },
];

export async function fetchBtcGlobal(): Promise<IBtcGlobal> {
  try {
    const response: Response = await fetch(BITBO_URL, {
      // next: { revalidate: 60 * 60 }, // Aggiorna la cache ogni 1 ora
    });
    const data: string = await response.text();

    const $ = cheerio.load(data);
    const categories: IBtcGlobalCategoryData[] = [];
    let summary: IBtcGlobalSummaryData | null = null;

    // Estrarre i dati della tabella principale (summary) con le classi specifiche
    const summaryRow = $(".top-table-data-row");
    if (summaryRow.length) {
      summary = {
        entities: summaryRow.find(".td-symbol").text().trim(),
        btcTotal: summaryRow
          .find(".td-company_btc")
          .text()
          .trim()
          .replace(/,/g, ""),
        valueToday: summaryRow
          .find(".td-value")
          .text()
          .trim()
          .replace(/,/g, ""),
        percentageOf21m: summaryRow.find(".td-company_percent").text().trim(),
        lastUpdated: summaryRow.find(".td-last-updated").text().trim(),
      };
    }

    // Estrarre i dati della tabella "Totals by Category"
    $(".treasuries-table--smaller tbody tr").each((index, element) => {
      const category: string =
        $(element).find("td.td-symbol a").text().trim() ||
        $(element).find("td.td-symbol").text().trim();
      const btcHoldings: string = $(element)
        .find("td.td-company_btc")
        .text()
        .trim()
        .replace(/,/g, "");
      const valueToday: string = $(element)
        .find("td.td-value")
        .text()
        .trim()
        .replace(/,/g, "");
      const percentageOf21m: string = $(element)
        .find("td.td-company_percent")
        .text()
        .trim();

      // Escludere il primo elemento se erroneamente catturato dalla tabella principale
      if (category && category !== summary?.entities) {
        categories.push({ category, btcHoldings, valueToday, percentageOf21m });
      }
    });

    return {
      summary,
      data: categories,
      history: BTCGLOBAL_HISTORY,
    } as IBtcGlobal;
  } catch (error) {
    console.error(
      "Errore durante il fetch o la validazione Bitbo Treasuries",
      error
    );
    throw new Error("Errore durante il recupero dei dati Bitbo Treasuries");
  }
}

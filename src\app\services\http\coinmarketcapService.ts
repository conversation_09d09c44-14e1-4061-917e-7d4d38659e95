import { ICoinmarketcapCoinsData } from "@/app/interfaces/coinmarketcap";
import CacheService from "./cacheService";

const COINMARKETCAP_URL =
  "https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest?aux=num_market_pairs,cmc_rank,date_added,tags,platform,max_supply,circulating_supply,total_supply,market_cap_by_total_supply,volume_24h_reported,volume_7d,volume_7d_reported,volume_30d,volume_30d_reported,is_market_cap_included_in_calc&limit=500";

export async function fetchCoinmarketcapCoinsData(): Promise<
  ICoinmarketcapCoinsData[]
> {
  return CacheService.fetchWithCache(
    {
      key: "coinmarketcap-coins-data",
      ttlMinutes: 1, // Cache per 1 minuto
    },
    async () => {
      try {
        console.log(
          "Effettuando una nuova chiamata a CoinMarketCap - Coins..."
        );
        const response = await fetch(COINMARKETCAP_URL, {
          // next: { revalidate: 60 * 15 }, // Aggiorna la cache ogni 15 minuti
          headers: {
            "X-CMC_PRO_API_KEY": process.env.COINMARKETCAP_API_KEY as string,
            "Content-Type": "application/json",
          },
        });

        const responseData = await response.json();
        const data: ICoinmarketcapCoinsData[] = responseData.data;

        if (!response.ok) {
          throw new Error("Errore durante il fetch da CoinMarketCap (Coins)");
        }
        return data;
      } catch (error) {
        console.error(
          "Errore durante il fetch o la validazione CoinMarketCap (Coins):",
          error
        );
        throw new Error(
          "Errore durante il recupero dei dati CoinMarketCap (Coins)"
        );
      }
    }
  );
}

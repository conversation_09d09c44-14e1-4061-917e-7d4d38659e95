"use client";
import {
  IBtcEtfHistory,
  ICombinedEtfHistory,
  IEthEtfHistory,
} from "@/app/interfaces/googleSheet";
import { formatMarketCap } from "@/app/utils/format-numbers/formatMarketCap";
import { Card, CardBody } from "@heroui/react";
import React, { useMemo, useState } from "react";
import DrawerEtf from "../drawers/DrawerEtf";
import { IconArrowRight } from "../shared/Icons";

export const EtfWidget = ({
  selectedName,
  etfHistory,
}: {
  selectedName: "btc" | "eth";
  etfHistory: {
    btcEtfHistory: IBtcEtfHistory[];
    ethEtfHistory: IEthEtfHistory[];
  };
}): React.ReactNode => {
  // console.log("etfHistory", etfHistory);

  const [drawerEtfIsOpen, setDrawerEtfIsOpen] = useState(false);

  const {
    selectedData,
    lastDataBtcEtfLast7days,
    lastDataEthEtfLast7days,
    formattedTotalLast7days,
    formattedTotal$Last7days,
  } = useMemo(() => {
    const lastDataBtcEtf =
      etfHistory.btcEtfHistory[etfHistory.btcEtfHistory.length - 1];

    const lastDataBtcEtfLast7days = etfHistory.btcEtfHistory
      .slice(
        etfHistory.btcEtfHistory.length - 7,
        etfHistory.btcEtfHistory.length
      )
      .reduce(
        (acc, curr) => ({
          total$: acc.total$ + curr.daily$,
          totalBtc: acc.totalBtc + curr.dailyBtc,
        }),
        { total$: 0, totalBtc: 0 }
      );

    const lastDataEthEtf =
      etfHistory.ethEtfHistory[etfHistory.ethEtfHistory.length - 1];

    const lastDataEthEtfLast7days = etfHistory.ethEtfHistory
      .slice(
        etfHistory.ethEtfHistory.length - 7,
        etfHistory.ethEtfHistory.length
      )
      .reduce(
        (acc, curr) => ({
          total$: acc.total$ + curr.daily$,
          totalEth: acc.totalEth + curr.dailyEth,
        }),
        { total$: 0, totalEth: 0 }
      );

    let formattedTotalLast7days;
    let formattedTotal$Last7days;

    const selectedData = (
      selectedName === "btc" ? lastDataBtcEtf : lastDataEthEtf
    ) as ICombinedEtfHistory;

    switch (selectedName) {
      case "btc":
        if (lastDataBtcEtfLast7days.totalBtc > 0) {
          formattedTotalLast7days = formatMarketCap(
            lastDataBtcEtfLast7days.totalBtc
          );
          formattedTotal$Last7days = formatMarketCap(
            lastDataBtcEtfLast7days.total$ * 1000000
          );
        } else {
          formattedTotalLast7days = formatMarketCap(
            +lastDataBtcEtfLast7days.totalBtc!.toString()!.slice(1)
          );
          formattedTotal$Last7days = formatMarketCap(
            +lastDataBtcEtfLast7days.total$!.toString()!.slice(1) * 1000000
          );
        }
        break;
      case "eth":
        if (lastDataEthEtfLast7days.totalEth > 0) {
          formattedTotalLast7days = formatMarketCap(
            lastDataEthEtfLast7days.totalEth!
          );
          formattedTotal$Last7days = formatMarketCap(
            lastDataEthEtfLast7days.total$ * 1000000
          );
        } else {
          formattedTotalLast7days = formatMarketCap(
            +lastDataEthEtfLast7days.totalEth!.toString()!.slice(1)
          );
          formattedTotal$Last7days = formatMarketCap(
            +lastDataEthEtfLast7days.total$!.toString()!.slice(1) * 1000000
          );
        }
        break;
      default:
        "";
        break;
    }

    return {
      selectedData,
      lastDataBtcEtfLast7days,
      lastDataEthEtfLast7days,
      formattedTotalLast7days,
      formattedTotal$Last7days,
    };
  }, [etfHistory, selectedName]);

  return (
    <>
      {drawerEtfIsOpen && (
        <DrawerEtf
          selectedData={selectedName}
          data={etfHistory!}
          isDrawerOpen={drawerEtfIsOpen}
          onClose={() => setDrawerEtfIsOpen(false)}
        ></DrawerEtf>
      )}
      <Card
        classNames={{
          base: "rounded-xl",
        }}
      >
        <CardBody className="p-3" onClick={() => setDrawerEtfIsOpen(true)}>
          <div className="flex">
            <span className="text-sm text-zinc-400 font-bold">
              {selectedName === "btc" ? "Bitcoin" : "Ethereum"} ETF
            </span>
            <IconArrowRight classNames="ml-2"></IconArrowRight>
          </div>
          <div className="grid grid-cols-[38px_auto_auto] mt-2">
            <div className="text-zinc-500 text-xs  border-b border-zinc-800 pb-1 mb-2">
              7 gg
            </div>
            <div
              className={`${
                selectedName === "btc" && lastDataBtcEtfLast7days.total$ > 0
                  ? "text-green-500"
                  : selectedName === "eth" && lastDataEthEtfLast7days.total$ > 0
                  ? "text-green-600"
                  : "text-red-600"
              } text-xs text-end border-b border-zinc-800 pb-1 mb-2 text-zinc-400}`}
            >
              {selectedName === "btc" && lastDataBtcEtfLast7days.totalBtc > 0
                ? ""
                : selectedName === "eth" && lastDataEthEtfLast7days.totalEth > 0
                ? ""
                : "- "}
              <span className={" text-xs mr-1"}>{formattedTotalLast7days}</span>
            </div>
            <div
              className={` ${
                selectedName === "btc" && lastDataBtcEtfLast7days.total$ > 0
                  ? "text-green-500"
                  : selectedName === "eth" && lastDataEthEtfLast7days.total$ > 0
                  ? "text-green-600"
                  : "text-red-600"
              } text-end text-xs border-b border-zinc-800 pb-1 mb-2`}
            >
              {selectedName === "btc" && lastDataBtcEtfLast7days.total$ > 0
                ? ""
                : selectedName === "eth" && lastDataEthEtfLast7days.total$ > 0
                ? ""
                : "- "}

              {formattedTotal$Last7days}
            </div>

            <div className="col-[1/2] font-semibold text-sm">Tot.</div>

            <div className="flex items-center justify-end">
              <span
                className={` ${
                  selectedName === "btc" ? "text-btcColor" : "text-blue-500"
                } text-sm mr-1`}
              >
                {selectedName === "btc" ? "₿" : "Ξ"}
              </span>
              <span className="text-sm font-semibold">
                {selectedName === "btc"
                  ? formatMarketCap(selectedData?.totalBtc)
                  : formatMarketCap(selectedData?.totalEth)}
              </span>
            </div>

            <div
              className={`${
                selectedName === "btc" && selectedData?.total$ > 0
                  ? "text-green-500"
                  : selectedName === "eth" && selectedData?.total$ > 0
                  ? "text-green-500"
                  : "text-red-500"
              } text-end text-sm font-semibold flex items-center justify-end`}
            >
              ${formatMarketCap(selectedData?.total$! * 1000000)}
            </div>

            {/* <div className="text-sm text-zinc-400">{selectedData.date}</div> */}
          </div>
        </CardBody>
      </Card>
    </>
  );
};

import { IBtcEtfHistory, IEthEtfHistory } from "@/app/interfaces/googleSheet";

const GOOGLE_SHEET_ETF_URL =
  "https://sheets.googleapis.com/v4/spreadsheets/1QO-h_7S4vMQEy3LxTvGFt7LaGfCD0NL_-jWsAJ1RF68/values/EtfBtc?alt=json&key=AIzaSyAhXXS5ctqKfELybuqs88pkDBLOWN8FGfs";

export async function fetchEtfHistory(): Promise<{
  btcEtfHistory: IBtcEtfHistory[];
  ethEtfHistory: IEthEtfHistory[];
}> {
  console.log("Effettuando una nuova chiamata a Google Sheet - ETF...");
  try {
    const response = await fetch(GOOGLE_SHEET_ETF_URL, {
      // next: { revalidate: 60 * 5 },
    }); // Aggiorna la cache ogni 5 minuti

    if (!response.ok) {
      throw new Error("Errore durante il fetch da Google Sheet (ETF)");
    }
    const responseData: {
      majorDimensios: string;
      range: string;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      values: any[];
    } = await response.json();

    responseData.values = responseData.values.slice(1);

    const _btcHistory: IBtcEtfHistory[] = [],
      _ethHistory: IEthEtfHistory[] = [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    responseData.values.forEach((item: any) => {
      _btcHistory.push({
        date: item[0].split("/").reverse().join("/"),
        daily$: +item[1],
        dailyBtc: +item[2],
        total$: +item[3],
        totalBtc: +item[4],
      });

      if (!!item[6]) {
        _ethHistory.push({
          date: item[6].split("/").reverse().join("/"),
          daily$: +item[7],
          dailyEth: +item[8],
          total$: +item[9],
          totalEth: +item[10],
        });
      }
    });

    const data: {
      btcEtfHistory: IBtcEtfHistory[];
      ethEtfHistory: IEthEtfHistory[];
    } = {
      btcEtfHistory: _btcHistory,
      ethEtfHistory: _ethHistory,
    };

    return data;
  } catch (error) {
    console.error(
      "Errore durante il fetch o la validazione  Google Sheet (ETF)",
      error
    );
    throw new Error("Errore durante il recupero dei dati  Google Sheet (ETF)");
  }
}

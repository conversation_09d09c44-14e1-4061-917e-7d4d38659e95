"use client";

import { Skeleton } from "@heroui/react";

export default function Loading() {
  const placeholders: number[] = [1, 2, 3, 4, 5, 6];

  return placeholders.map((item: number) => (
    <div className="space-y-3 mt-4 px-3 py-0 pb-4" key={item}>
      <Skeleton className="w-100 rounded-lg">
        <div className="h-4 w-100 rounded-lg bg-default-300" />
      </Skeleton>
      <Skeleton className="w-100 rounded-lg">
        <div className="h-4 w-100 rounded-lg bg-default-200" />
      </Skeleton>
      <Skeleton className="w-100 rounded-lg">
        <div className="h-4 w-100 rounded-lg bg-default-200" />
      </Skeleton>
    </div>
  ));
}

'use client';

import { useState, useEffect } from 'react';
import CacheService from '@/app/services/http/cacheService';

interface CacheEntry {
  key: string;
  expiresAt: Date;
  age: number;
  sizeKB: number;
}

export function CacheManager() {
  const [cacheEntries, setCacheEntries] = useState<CacheEntry[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  const loadCacheEntries = () => {
    if (typeof window === 'undefined') return;

    const entries: CacheEntry[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        try {
          const item = localStorage.getItem(key);
          if (item) {
            const parsed = JSON.parse(item);
            // Check if it's a cache item (has timestamp and expiresAt)
            if (parsed.timestamp && parsed.expiresAt) {
              const info = CacheService.getCacheInfo(key);
              if (info.exists) {
                entries.push({
                  key,
                  expiresAt: info.expiresAt!,
                  age: info.age!,
                  sizeKB: Math.round((item.length * 2) / 1024) // Rough estimate
                });
              }
            }
          }
        } catch {
          // Skip non-JSON items
        }
      }
    }
    setCacheEntries(entries);
  };

  useEffect(() => {
    if (isVisible) {
      loadCacheEntries();
      const interval = setInterval(loadCacheEntries, 1000);
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const clearAllCache = () => {
    cacheEntries.forEach(entry => {
      CacheService.clearCache(entry.key);
    });
    loadCacheEntries();
  };

  const clearSpecificCache = (key: string) => {
    CacheService.clearCache(key);
    loadCacheEntries();
  };

  const formatAge = (ageMs: number) => {
    const seconds = Math.floor(ageMs / 1000);
    const minutes = Math.floor(seconds / 60);
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  };

  const formatTimeUntilExpiry = (expiresAt: Date) => {
    const now = new Date();
    const diff = expiresAt.getTime() - now.getTime();
    if (diff <= 0) return 'Scaduto';
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors z-50"
      >
        🗂️ Cache Manager
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md w-full max-h-96 overflow-y-auto z-50">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-gray-800">Cache Manager</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      <div className="mb-3">
        <button
          onClick={clearAllCache}
          className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors mr-2"
        >
          Pulisci tutto
        </button>
        <button
          onClick={loadCacheEntries}
          className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors"
        >
          Aggiorna
        </button>
      </div>

      {cacheEntries.length === 0 ? (
        <p className="text-gray-500 text-sm">Nessuna cache trovata</p>
      ) : (
        <div className="space-y-2">
          {cacheEntries.map((entry) => (
            <div key={entry.key} className="border border-gray-200 rounded p-2 text-xs">
              <div className="flex justify-between items-start mb-1">
                <span className="font-medium text-gray-800 truncate flex-1">
                  {entry.key}
                </span>
                <button
                  onClick={() => clearSpecificCache(entry.key)}
                  className="text-red-600 hover:text-red-800 ml-2"
                  title="Elimina cache"
                >
                  🗑️
                </button>
              </div>
              <div className="text-gray-600 space-y-1">
                <div>Età: {formatAge(entry.age)}</div>
                <div>Scade tra: {formatTimeUntilExpiry(entry.expiresAt)}</div>
                <div>Dimensione: {entry.sizeKB} KB</div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500">
        Totale: {cacheEntries.length} voci, {cacheEntries.reduce((sum, entry) => sum + entry.sizeKB, 0)} KB
      </div>
    </div>
  );
}

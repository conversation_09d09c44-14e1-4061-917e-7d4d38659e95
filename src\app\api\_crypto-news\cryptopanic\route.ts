import { ICryptoPanicNews } from "@/app/interfaces/cryptopanic";
import { NextResponse } from "next/server";

// Variabili per la cache lato server
let cachedData: ICryptoPanicNews[] | null = null;
let lastFetchTime = 0;

export const GET = async (): Promise<
  NextResponse<
    { data: ICryptoPanicNews[] } | ICryptoPanicNews[] | { error: string }
  >
> => {
  const CRYPTOPANIC_API_URL =
    "https://cryptopanic.com/api/v1/posts/?auth_token=1a7949f5825f7e3fd2e670bd54e7b2a680593f02&kind=news&filter=important&public=true";

  const CACHE_DURATION = 60 * 1000 * 5; // Cache valida per 5 minuti

  const currentTime = Date.now();

  // Controlla se i dati nella cache sono ancora validi
  if (cachedData && currentTime - lastFetchTime < CACHE_DURATION) {
    console.log("Servendo dati dalla cache lato server (Cryptopanic News)");

    return NextResponse.json(cachedData);
  }

  // Effettua una nuova chiamata API
  try {
    console.log("Effettuando una nuova chiamata a CryptoPanic News...");

    const response = await fetch(CRYPTOPANIC_API_URL);

    const responseData = await response.json();
    const data: ICryptoPanicNews[] = responseData.results;

    // Aggiorna la cache
    cachedData = data;
    lastFetchTime = currentTime;

    // console.log("Chiamata a Cryptonews andata a buon fine!");

    // console.log("CryptoPanic news", data);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Errore durante il fetch da Cryptonews:", {
      message: error.message,
      stack: error.stack,
    });
    return NextResponse.json(
      { error: "Errore durante il recupero dei dati CryptoPanic News" },
      { status: 500 }
    );
  }
};

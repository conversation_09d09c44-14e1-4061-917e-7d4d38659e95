import { GoogleGenerativeAI } from "@google/generative-ai";
import {
  getLatestGeminiSummary,
  saveGeminiSummary,
} from "../db/supabaseService";

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

/**
 * Funzione che prende un array di articoli, li unisce e li invia a Gemini per un riassunto.
 * @param articles - Lista di articoli da unire e riassumere.
 * @returns Il riassunto generato da Gemini.
 */
export async function summarizeArticles(articles: string[]): Promise<{
  summary: string;
  sentiment: string;
  coin_tickers: string[];
  created_at: string;
}> {
  if (articles.length === 0) {
    throw new Error("Nessun articolo fornito.");
  }

  // Unisce gli articoli con formattazione
  const combinedText = articles
    .reverse()
    .map((article) => `${article}`)
    .join("\n");

  console.log("lunghezza articoli supabase", combinedText);

  // Prompt per Gemini

  //  BUONO
  // const prompt = `Fai un brevissimo riassunto sugli argomenti chiave (queste sono ultime news del mondo cripto) e alla fine scrivi solo "POSITIVO" o "NEUTRO" o "NEGATIVO", mi serve come piccolo testo da mettere in alto su un sito di criptovalute:\n\n${combinedText}`;

  // const prompt = `
  // Parla molto brevemente dei temi delle ultime notizie che interessano al mondo delle criptovalute (in massimo 150 caratteri) di questi giorni e all'inizio scrivi solo a livello generale "SENTIMENT: POSITIVO/NEUTRO/NEGATIVO",(non fare introduzioni tipo "ecco i temi principali:" ecc., vai a capo dopo ogni frase .,il sentiment vai a capo con un solo <br>, le altre ccon doppio <br>, e solo il valore del sentiment deve avere un colore (green-yellow-red), il formato è testo ma aggiungi elementi html, css in linea). Ecco le ultime news:\n\n${combinedText}`;

  //   const prompt = `
  // Riassumi molto brevemente i temi principali delle ultime notizie sulle criptovalute (massimo 150 caratteri) e fornisci anche un sentiment generale (POSITIVO/NEUTRO/NEGATIVO).
  // Restituisci il risultato in formato JSON, esattamente come nell'esempio seguente, senza alcun testo aggiuntivo:
  // {
  //   "summary": "<il riassunto>",
  //   "sentiment": "POSITIVO"
  // }

  // Ecco le ultime news:
  // ${combinedText}
  // `;

  const prompt = `
Fai un riassunto chiaro delle ultime notizie sulle criptovalute senza introduzioni tipo "ecco il riassunto sulle cripto,ecc." (massimo 200 caratteri) e fornisci anche un sentiment generale (POSITIVO/NEUTRO/NEGATIVO) e le criptovalute citate nelle varie notizie (campo coin_tickers, non ripetere lo stesso ticker più volte, in formato string[] in ordine in base alle notizie più recenti).
Restituisci il risultato in formato JSON, esattamente come nell'esempio seguente, senza alcun testo aggiuntivo:
{
  "summary": "<il riassunto di tutte le news>",
  "sentiment": "POSITIVO",
  "coin_tickers": ["BTC, ETH"],
}
  
Ecco le ultime news:
${combinedText}
`;

  try {
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
    const result = await model.generateContent(prompt);
    const rawText = result.response.text();

    // Rimuove i delimitatori markdown (```json all'inizio e ``` alla fine)
    const cleanText = rawText
      .replace(/```json\s*/, "")
      .replace(/```$/, "")
      .trim();

    console.log("cleanText", cleanText);

    try {
      const jsonResult: {
        summary: string;
        sentiment: string;
        coin_tickers: string[];
        created_at: string;
      } = JSON.parse(cleanText);

      let summaryText: string = jsonResult.summary;
      // Esempio di post-elaborazione: aggiunge  dopo ogni punto seguito da uno spazio
      summaryText = summaryText.replace(/\. /g, ".\n");
      jsonResult.summary = summaryText;

      if (jsonResult.summary && jsonResult.sentiment) {
        return jsonResult;
      } else {
        throw new Error("Il JSON non contiene i campi richiesti.");
      }
    } catch (parseError) {
      console.error("Errore nel parsing della risposta JSON:", parseError);
      throw new Error("La risposta di Gemini non è in formato JSON valido.");
    }
  } catch (error) {
    console.error("Errore durante la chiamata a Gemini:", error);
    throw new Error("Errore nell'analisi degli articoli.");
  }
}
//   try {
//     const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
//     const result = await model.generateContent([prompt]);

//     return result.response.text() || "Riassunto non disponibile.";
//   } catch (error) {
//     console.error("Errore durante la chiamata a Gemini:", error);
//     throw new Error("Errore nell'analisi degli articoli.");
//   }
// }

const GEMINI_CACHE_DURATION_MS = 1000 * 60 * 60; // 1 ora

async function generateAndSaveGeminiSummary(newsTitles: string[]) {
  const newSummary = await summarizeArticles(newsTitles);
  await saveGeminiSummary(newSummary.summary, newSummary.sentiment, newsTitles);
  return newSummary;
}

export async function fetchOrGenerateGeminiSummary(newsTitles: string[]) {
  const latestSummary = await getLatestGeminiSummary();

  if (!latestSummary) {
    console.log("🆕 Nessun riassunto trovato, generazione nuovo...");
    return await generateAndSaveGeminiSummary(newsTitles);
  }

  const currentTime = Date.now();
  const lastFetchTime = new Date(latestSummary.created_at).getTime();
  const timeDifference = currentTime - lastFetchTime;

  if (timeDifference < GEMINI_CACHE_DURATION_MS) {
    console.log("🔄 Servendo Gemini Summary dalla cache DB", latestSummary);
    return latestSummary;
  }

  console.log("🆕 Cache scaduta, generazione nuovo riassunto con Gemini...");
  return await generateAndSaveGeminiSummary(newsTitles);
}

"use client";
import { ICoingeckoCoinsData } from "@/app/interfaces/coingecko";
import { ICoinmarketcapCoinsData } from "@/app/interfaces/coinmarketcap";
import { Card, CardBody } from "@heroui/react";
import Image from "next/image";

export const Top3Coins = ({
  range,
  coinmarketcapCoinsData,
  coingeckoCoinsData,
}: {
  range: "7gg" | "30gg";
  coinmarketcapCoinsData: ICoinmarketcapCoinsData[];
  coingeckoCoinsData: ICoingeckoCoinsData[];
}): React.ReactNode => {
  const top3coins7d = coinmarketcapCoinsData
    .filter((item) => item.cmc_rank <= 20)
    .sort(
      (item1, item2) =>
        item2.quote.USD.percent_change_7d! - item1.quote.USD.percent_change_7d!
    )
    .slice(0, 3);
  const top3coins30d = coinmarketcapCoinsData
    .filter((item) => item.cmc_rank <= 20)
    .sort(
      (item1, item2) =>
        item2.quote.USD.percent_change_30d! -
        item1.quote.USD.percent_change_30d!
    )
    .slice(0, 3);

  const filteredCoins = range === "7gg" ? top3coins7d : top3coins30d;

  const images: string[] = [];

  filteredCoins.forEach((item) => {
    const imageUrl = coingeckoCoinsData?.find(
      (coin: ICoingeckoCoinsData) =>
        coin.symbol.toLowerCase() ===
        (item.symbol?.toLowerCase() || item.name?.toLowerCase())
    )?.image;

    images.push(imageUrl!);
  });

  return (
    <Card
      classNames={{
        base: "rounded-xl",
      }}
    >
      <CardBody className="p-3">
        <div className="flex">
          <span className="text-sm text-zinc-400 font-bold mb-1">
            Migliori - {range}
          </span>
          {/* <IconArrowRight classNames="ml-2"></IconArrowRight> */}
        </div>
        <div className="flex flex-col justify-start w-100 mt-1">
          {filteredCoins.map((item, index) => (
            <div
              key={index}
              className="text-sm font-bold grid grid-cols-[24px_45px_auto] grid-rows-[24px] w-100 items-center gap-1 "
            >
              {/* <span className="text-xs text-zinc-400"> #{item.cmc_rank}</span> */}
              <div className="bg-black rounded-full w-[20px] h-[20px]">
                <Image
                  src={images[index]}
                  alt={item.name}
                  width={20}
                  height={20}
                  className="rounded-full"
                ></Image>
              </div>
              <span className="text-sm"> {item.symbol}</span>
              {/* <span className="text-sm text-end font-medium text-zinc-400">
                  {formatMarketCap(item.quote.USD.market_cap)}
                </span> */}
              <span
                className={`text-sm text-end ${
                  (
                    range === "7gg"
                      ? item.quote.USD.percent_change_7d < 0
                      : item.quote.USD.percent_change_30d < 0
                  )
                    ? "text-red-500"
                    : "text-green-500"
                } `}
              >
                {(range === "7gg"
                  ? item.quote.USD.percent_change_7d
                  : item.quote.USD.percent_change_30d
                )
                  .toFixed(1)
                  .toString()
                  .replace(".", ",")}{" "}
                %
              </span>
            </div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
};

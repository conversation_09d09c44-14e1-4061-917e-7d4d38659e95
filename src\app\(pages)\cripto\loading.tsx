"use client";

import { Skeleton } from "@heroui/react";

export default function Loading() {
  return (
    <>
      <p className="m-3 text-sm text-zinc-500">
        Le criptovalute sono una forma di moneta digitale che si basa su un{" "}
        <span className="text-sm font-bold text-zinc-300">
          sistema decentralizzato
        </span>
        , senza l’intervento di banche centrali o governi.
        <br /> <br />
        La loro esistenza è resa possibile dalla tecnologia{" "}
        <span className="text-sm font-bold text-zinc-300">blockchain</span>, un
        registro distribuito che garantisce la sicurezza e l’immutabilità delle
        transazioni.
        {/* <br />
        <br />
        L’uso delle criptovalute e della blockchain ha trovato applicazioni in
        diversi settori: */}
      </p>
      <div className="border-1 border-zinc-800 rounded-xl p-0 m-3 relative overflow-hidden  min-h-[800px]">
        {/* <h2 className="text-xl font-bold text-center p-2 bg-tableHeaderBg sticky left-0 rounded-t-xl w-100">
          Top Cripto
        </h2> */}
        <Skeleton className="min-h-[800px]"></Skeleton>
      </div>
    </>
  );
}

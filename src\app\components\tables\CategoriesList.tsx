"use client";
import { ICoinmarketcapCoinsData } from "@/app/interfaces/coinmarketcap";
import { ICryptoRankCoinsData } from "@/app/interfaces/cryptorank";
import { formatMarketCap } from "@/app/utils/format-numbers/formatMarketCap";
import {
  Modal,
  ModalBody,
  ModalContent,
  ModalHeader,
  Progress,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  useDisclosure,
} from "@heroui/react";
import React, { useMemo, useState } from "react";

interface ICategories {
  id: number;
  key: string;
  name: string;
  numberOfProjects: number;
  coins?: any[]; //  ICryptoRankData[] extends QUOTE from coinmarketcap
  marketCap?: number;
  percent_change_30d?: number;
  percent_change_7d?: number;
  percent_change_24h?: number;
  percent_change_90d?: number;
  percent_change_60d?: number;
  gainers_30d?: any[];
}

export default function CategoriesList({
  cryptorankCoinsData,
  coinmarketcapCoinsData,
}: {
  cryptorankCoinsData: ICryptoRankCoinsData[];
  coinmarketcapCoinsData: ICoinmarketcapCoinsData[];
}): React.ReactNode {
  let categories: ICategories[] = [];

  cryptorankCoinsData.forEach((item: ICryptoRankCoinsData) => {
    const category = item.category;
    if (category && category.name.toLowerCase() === "stablecoin") return;
    if (category && !categories.find((c) => c.name === category!.name)) {
      categories.push({
        ...category,
        coins: [],
        marketCap: 0,
        gainers_30d: [],
      });
    }
    const foundCategory = categories.find((cat) => cat.name === category?.name);
    if (foundCategory) {
      const foundItem = {
        ...item,
        quote: coinmarketcapCoinsData?.find(
          (coin) => coin.symbol.toLowerCase() === item.symbol?.toLowerCase()
        )?.quote,
      };

      foundCategory.coins?.push(foundItem || item);

      foundCategory.marketCap = foundCategory.marketCap! + +item.marketCap!;
    }
  });

  categories.forEach((category, index) => {
    switch (category.name.toLowerCase()) {
      case "currency":
        category.name = "Monete";
        break;
      case "chain":
        category.name = "Blockchain";
        break;

      case "blockchain infrastructure":
        category.name = "Infrastruttura";
        break;
      case "blockchain service":
        category.name = "Servizi Blockchain";
        break;

      default:
        break;
    }

    let percent_change_24h: number[] = [];
    let percent_change_7d: number[] = [];
    let percent_change_30d: number[] = [];
    let percent_change_60d: number[] = [];
    let percent_change_90d: number[] = [];

    category.coins?.forEach((coin) => {
      if (coin.quote) {
        percent_change_24h.push(coin.quote.USD.percent_change_24h);
        percent_change_7d.push(coin.quote.USD.percent_change_7d);
        percent_change_30d.push(coin.quote.USD.percent_change_30d);
        percent_change_60d.push(coin.quote.USD.percent_change_60d);
        percent_change_90d.push(coin.quote.USD.percent_change_90d);

        // if (+coin.quote.USD.percent_change_30d > 0)
        //   categories[index].gainers_30d?.push(coin.symbol);

        categories[index].percent_change_24h = +(
          percent_change_24h.reduce((a, b) => a + b, 0) /
          percent_change_24h.length
        )
          .toFixed(2)
          .toString();

        categories[index].percent_change_7d = +(
          percent_change_7d.reduce((a, b) => a + b, 0) /
          percent_change_7d.length
        ).toFixed(2);

        categories[index].percent_change_30d = +(
          percent_change_30d.reduce((a, b) => a + b, 0) /
          percent_change_30d.length
        ).toFixed(2);

        categories[index].percent_change_60d = +(
          percent_change_60d.reduce((a, b) => a + b, 0) /
          percent_change_60d.length
        ).toFixed(2);

        categories[index].percent_change_90d = +(
          percent_change_90d.reduce((a, b) => a + b, 0) /
          percent_change_90d.length
        ).toFixed(2);
      }
    });
  });

  // console.log("categories", categories);

  // console.log("cryptorankCategories", cryptorankCategories);

  interface CategoriesTableProps {
    filteredCategories: ICategories[];
  }

  const CategoriesTabel: React.FC<CategoriesTableProps> = ({
    filteredCategories,
  }) => {
    const columns = [
      { uid: "position", name: "#" },
      { uid: "image", name: "" },
      { uid: "name", name: "Categoria" },
      { uid: "marketCap", name: "Cap. M." },
      { uid: "marketCapPercent", name: "Dominanza" },
      { uid: "cripto", name: "# Cripto" },
      { uid: "percent_change_7d", name: "7 gg" },
      { uid: "percent_change_30d", name: "30 gg" },
      { uid: "percent_change_90d", name: "90 gg" },
    ];

    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [page, setPage] = useState(1);
    const pages = Math.ceil(filteredCategories.length / rowsPerPage);
    const items = useMemo(() => {
      const start = (page - 1) * rowsPerPage;
      const end = start + rowsPerPage;

      return filteredCategories.slice(start, end);
    }, [page, filteredCategories, rowsPerPage]);

    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [modalCategory, setModalCategory] = useState<ICategories | null>(
      null
    );

    // console.log("filteredCategories", filteredCategories);

    const renderCell = (
      item: ICategories,
      columnKey: keyof any | string
    ): React.ReactNode => {
      switch (columnKey) {
        case "position":
          return (
            <div className="text-zinc-500">
              {filteredCategories.indexOf(item) + 1}
            </div>
          );
        case "image":
          return (
            <div className="flex">
              {item.coins!.slice(0, 3).map((coin, index: number) => (
                <img
                  src={coin.images!.native}
                  alt={index.toString()}
                  key={index}
                  className={`rounded-full h-[20px] w-[20px] ml-[-6px] bg-black border-1 border-zinc-700 z${
                    index + 1
                  }0`}
                ></img>
              ))}
            </div>
          );
        case "name":
          return (
            <div
              className="align-middle w-100 truncate text-md  cursor-pointer text-blue-600"
              onClick={() => {
                setIsModalOpen(true);
                setModalCategory(item);
              }}
            >
              {item.name}
            </div>
          );

        case "marketCap":
          return formatMarketCap(item.marketCap!);
        // <>
        //   <Progress
        //     aria-label="Dominanza"
        //     size="sm"
        //     value={item.market_cap}
        //     label={formatMarketCap(item.market_cap)}
        //     // showValueLabel={true}
        //     classNames={{
        //       label: "flex-1",
        //     }}
        //   />
        // </>

        case "marketCapPercent":
          const totalMarketCap = categories.reduce(
            (total, category) => total + category.marketCap!,
            0
          );

          return (
            <>
              <Progress
                aria-label="Dominanza"
                size="sm"
                value={(item.marketCap! / totalMarketCap) * 100}
                label={
                  (+(item.marketCap! / totalMarketCap) * 100)
                    .toFixed(1)
                    .toString()
                    .replace(".", ",") + " %"
                }
                // showValueLabel={true}
                classNames={{
                  label: "flex-1",
                }}
              />
            </>
          );
        //  return +((item.marketCap! / totalMarketCap) * 100).toFixed(1) + " %";

        case "cripto":
          return item.coins!.length;
        // return (
        //    <div
        //      className={
        //        item.market_cap_change_24h > 0
        //          ? "text-green-500"
        //          : "text-red-500"
        //      }
        //    >
        //      {item.market_cap_change_24h.toFixed(1) + "%"}
        //    </div>
        // );

        case "percent_change_7d":
          return (
            <span
              className={
                item.percent_change_7d! > 0 ? "text-green-500" : "text-red-500"
              }
            >
              {item.percent_change_7d!.toFixed(1).toString().replace(".", ",") +
                " %"}
            </span>
          );

        case "percent_change_30d":
          return (
            <span
              className={
                item.percent_change_30d! > 0 ? "text-green-500" : "text-red-500"
              }
            >
              {item
                .percent_change_30d!.toFixed(1)
                .toString()
                .replace(".", ",") + " %"}
            </span>
          );
        case "percent_change_90d":
          return (
            <span
              className={
                item.percent_change_90d! > 0 ? "text-green-500" : "text-red-500"
              }
            >
              {item
                .percent_change_90d!.toFixed(1)
                .toString()
                .replace(".", ",") + " %"}
            </span>
          );

        default:
          return null;
      }
    };

    // const TopContent = memo(() => {
    //   return (
    //     <h2 className="text-xl font-bold text-center p-2 bg-tableHeaderBg sticky left-0 rounded-t-xl w-100">
    //       Top Categorie
    //     </h2>
    //   );
    // });

    // const BottomContent = memo(() => {
    //   return (
    //     <div className="py-2  flex justify-center items-center sticky left-0">
    //       <Pagination
    //         showControls
    //         size="sm"
    //         classNames={{
    //           cursor: "bg-foreground text-background",
    //         }}
    //         color="default"
    //         page={page}
    //         total={pages}
    //         variant="light"
    //         onChange={setPage}
    //       />
    //     </div>
    //   );
    // });

    const classNames = useMemo(
      () => ({
        wrapper: [
          // "max-h-[600px]",
          "p-0",
          "border-0",
          "overflow-x-auto",
          "overflow-y-hidden",
          // "bg-black",
        ],
        table: [],
        th: [
          "bg-transparent",
          "text-default-500",
          "border-b",
          "border-divider",
        ],
        td: [
          // changing the rows border radius
          // first
          "group-data-[first=true]/tr:first:before:rounded-none",
          "group-data-[first=true]/tr:last:before:rounded-none",
          // middle
          "group-data-[middle=true]/tr:before:rounded-none",
          // last
          "group-data-[last=true]/tr:first:before:rounded-none",
          "group-data-[last=true]/tr:last:before:rounded-none",
        ],
      }),
      []
    );

    return (
      <>
        <CategoryDetailModal
          isOpen={isModalOpen}
          category={modalCategory!}
          onModalClose={() => setIsModalOpen(false)}
        ></CategoryDetailModal>
        <div className="border-1 border-zinc-800 rounded-xl p-0 m-2 relative overflow-hidden tablet:[margin-right:-20px]">
          {/* <TopContent></TopContent> */}
          <div className="overflow-x-auto tablet:[width:calc(100%-20px)]">
            <Table
              removeWrapper
              title="Top Categorie"
              aria-label="Tabella delle blockchain"
              className="text-white w-full table-auto p-0 m-0"
              classNames={classNames}
            >
              <TableHeader columns={columns}>
                {(column) => (
                  <TableColumn
                    key={column.uid}
                    align="start"
                    className={` whitespace-nowrap  ${
                      column.uid === "cripto"
                        ? "text-end min-w-[90px] max-w-[90px]"
                        : column.uid === "marketCapPercent"
                        ? "text-end min-w-[80px] max-w-[80px]"
                        : column.uid === "marketCapChange24h"
                        ? "mr-0 text-end"
                        : column.uid === "marketCap"
                        ? "mr-0 text-end pl-6 min-w-[80px] max-w-[80px]"
                        : column.uid === "position"
                        ? "min-w-[32px] px-2 text-end"
                        : column.uid === "image"
                        ? "min-w-[50px] max-w-[50px] p-2"
                        : column.uid === "name"
                        ? "sticky left-0 bg-tableBg p-2 z-10 min-w-[110px] max-w-[110px] pl-2"
                        : "text-end min-w-[100px] max-w-[100px] p-2"
                    } `}
                  >
                    {column.name}
                  </TableColumn>
                )}
              </TableHeader>
              <TableBody items={items}>
                {(item: ICategories) => (
                  <TableRow key={item.id}>
                    {(columnKey) => (
                      <TableCell
                        className={`border-b border-zinc-800
                     ${
                       columnKey === "cripto"
                         ? "text-end min-w-[85px] max-w-[85px]"
                         : columnKey === "marketCapPercent"
                         ? "text-end min-w-[80px] max-w-[80px] pl-4"
                         : columnKey === "marketCapChange24h"
                         ? "mr-[8px] text-end min-w-[100px] max-w-[100px]"
                         : columnKey === "marketCap"
                         ? "text-end p-2 min-w-[80px] max-w-[80px]"
                         : columnKey === "position"
                         ? "min-w-[32px] px-2 text-end"
                         : columnKey === "image"
                         ? "min-w-[50px] max-w-[50px] p-2 pr-0"
                         : columnKey === "name"
                         ? "sticky left-0 z-10 bg-tableBg  min-w-[120px] max-w-[120px] p-2"
                         : "text-end px-2 min-w-[100px] max-w-[100px]"
                     }`}
                        // style={
                        //   columnKey === "name"
                        //     ? {
                        //         boxShadow: "2px 0 2px -1px #e5e7eb",
                        //       }
                        //     : undefined
                        // }
                      >
                        {renderCell(item, columnKey.toString())}
                      </TableCell>
                    )}
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          {/* <BottomContent></BottomContent> */}
        </div>
      </>
    );
  };

  return (
    <div className="overflow-hidden">
      <CategoriesTabel filteredCategories={categories!}></CategoriesTabel>
    </div>
  );
}

function CategoryDetailModal({
  category,
  isOpen,
  onModalClose,
}: {
  category: ICategories;
  isOpen: boolean;
  onModalClose: () => void;
}): React.ReactNode {
  const { onOpenChange } = useDisclosure();
  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={onModalClose}
      scrollBehavior="inside"
      placement="bottom"
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1 text-center">
              {category.name}
            </ModalHeader>
            <ModalBody>
              <div className="flex flex-col gap-2 items-center">
                <div className="grid grid-cols-[40px_35px_45px_100px] grid-rows-1 gap-2 items-center font-bold">
                  <div className="text-center">POS.</div>
                  <div className="text-end ml-2">CRIPTO</div>
                  <div></div>
                  <div className="text-end ">CAP. M.</div>
                </div>
              </div>

              <div className="flex flex-col gap-2 items-center">
                {category.coins?.map((coin: any) => (
                  <div
                    key={coin.id}
                    className="grid grid-cols-[40px_35px_45px_100px] grid-rows-1 gap-2 items-center"
                  >
                    <div className="text-center text-zinc-500">{coin.rank}</div>
                    <img
                      src={coin.images.native}
                      alt={coin.name}
                      className="w-[22px] h-[22px] justify-self-center p-0"
                    ></img>
                    <div>{coin.symbol.toUpperCase()}</div>
                    <div className="text-end">
                      {formatMarketCap(coin.marketCap)}
                    </div>
                  </div>
                ))}
              </div>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

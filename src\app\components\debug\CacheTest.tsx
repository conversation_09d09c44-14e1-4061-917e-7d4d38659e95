'use client';

import { useCoingeckoCoins, useCoinmarketcapCoins } from '@/app/hooks/useApiWithCache';
import { useState } from 'react';

export function CacheTest() {
  const [showTest, setShowTest] = useState(false);
  
  const { 
    data: coingeckoData, 
    loading: coingeckoLoading, 
    error: coingeckoError,
    refetch: refetchCoingecko 
  } = useCoingeckoCoins();

  const { 
    data: coinmarketcapData, 
    loading: coinmarketcapLoading, 
    error: coinmarketcapError,
    refetch: refetchCoinmarketcap 
  } = useCoinmarketcapCoins();

  if (!showTest) {
    return (
      <button
        onClick={() => setShowTest(true)}
        className="fixed bottom-20 right-4 bg-green-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-green-700 transition-colors z-50"
      >
        🧪 Cache Test
      </button>
    );
  }

  return (
    <div className="fixed bottom-20 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md w-full max-h-96 overflow-y-auto z-50">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-gray-800">Cache Test</h3>
        <button
          onClick={() => setShowTest(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      <div className="space-y-4 text-sm">
        {/* CoinGecko Test */}
        <div className="border border-gray-200 rounded p-3">
          <div className="flex justify-between items-center mb-2">
            <h4 className="font-semibold text-gray-700">CoinGecko</h4>
            <button
              onClick={refetchCoingecko}
              className="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600"
              disabled={coingeckoLoading}
            >
              {coingeckoLoading ? '⏳' : '🔄'}
            </button>
          </div>
          
          {coingeckoLoading && <p className="text-blue-600">Caricamento...</p>}
          {coingeckoError && <p className="text-red-600">Errore: {coingeckoError.message}</p>}
          {coingeckoData && (
            <div className="text-gray-600">
              <p>✅ Dati caricati: {coingeckoData.length} coins</p>
              <p className="text-xs">Primo coin: {coingeckoData[0]?.name}</p>
            </div>
          )}
        </div>

        {/* CoinMarketCap Test */}
        <div className="border border-gray-200 rounded p-3">
          <div className="flex justify-between items-center mb-2">
            <h4 className="font-semibold text-gray-700">CoinMarketCap</h4>
            <button
              onClick={refetchCoinmarketcap}
              className="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600"
              disabled={coinmarketcapLoading}
            >
              {coinmarketcapLoading ? '⏳' : '🔄'}
            </button>
          </div>
          
          {coinmarketcapLoading && <p className="text-blue-600">Caricamento...</p>}
          {coinmarketcapError && <p className="text-red-600">Errore: {coinmarketcapError.message}</p>}
          {coinmarketcapData && (
            <div className="text-gray-600">
              <p>✅ Dati caricati: {coinmarketcapData.length} coins</p>
              <p className="text-xs">Primo coin: {coinmarketcapData[0]?.name}</p>
            </div>
          )}
        </div>

        {/* Istruzioni */}
        <div className="bg-gray-100 p-3 rounded text-xs">
          <h5 className="font-semibold mb-1">Come testare la cache:</h5>
          <ol className="list-decimal list-inside space-y-1">
            <li>Clicca 🔄 per fare una chiamata API</li>
            <li>Apri DevTools → Console per vedere i log</li>
            <li>Clicca di nuovo 🔄 entro 1 minuto</li>
            <li>Dovresti vedere "📦 Cache HIT" nella console</li>
            <li>Apri Cache Manager per vedere i dati salvati</li>
          </ol>
        </div>
      </div>
    </div>
  );
}

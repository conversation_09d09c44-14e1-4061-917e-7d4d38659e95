"use client";

import { ICoingeckoCoinsData } from "@/app/interfaces/coingecko";
import { ICoinmarketcapCoinsData } from "@/app/interfaces/coinmarketcap";
import { formatCurrency } from "@/app/utils/format-numbers/formatCurrency";
import { Card, CardBody } from "@heroui/react";
import Image from "next/image";

export const TopMarketCapWidget = ({
  coinmarketcapCoinsData,
  coingeckoCoinsData,
}: {
  coinmarketcapCoinsData: ICoinmarketcapCoinsData[];
  coingeckoCoinsData: ICoingeckoCoinsData[];
}): React.ReactNode => {
  const filteredTop5 = coinmarketcapCoinsData.slice(0, 5);

  let images: string[] = [];

  filteredTop5.forEach((item) => {
    let imageUrl = coingeckoCoinsData?.find(
      (coin: ICoingeckoCoinsData) =>
        coin.symbol.toLowerCase() ===
        (item.symbol?.toLowerCase() || item.name?.toLowerCase())
    )?.image;

    images.push(imageUrl!);
  });

  return (
    <Card
      classNames={{
        base: "rounded-xl",
      }}
    >
      <CardBody className="p-3">
        <div className="grid grid-cols-[21px_38px_auto_15%_15%_15%] gap-2  text-zinc-400 font-bold mb-0 border-b border-zinc-800 pb-1">
          <span className="col-[1/3] text-sm ">TOP 5</span>
          <span className="text-sm text-end">Prezzo</span>
          <span className="text-sm text-end">7gg</span>
          <span className="text-sm text-end">30gg</span>
          <span className="text-sm text-end">90gg</span>
          {/* <IconArrowRight classNames="ml-2"></IconArrowRight> */}
        </div>
        <div className="flex flex-col justify-start w-100 mt-1">
          {filteredTop5.map((item, index) => (
            <div
              key={index}
              className="text-sm grid grid-cols-[21px_38px_auto_15%_15%_15%] grid-rows-[30px] w-100 items-center gap-2 "
            >
              {/* <span className="text-xs text-zinc-400"> #{item.cmc_rank}</span> */}
              <div className="bg-black rounded-full w-[20px] h-[20px]">
                <Image
                  src={images[index]}
                  alt={item.name}
                  width={20}
                  height={20}
                  className="rounded-full"
                ></Image>
              </div>
              <span className="text-sm font-bold"> {item.symbol}</span>
              {/* <span className="text-sm text-end font-medium text-zinc-400">
                  {formatMarketCap(item.quote.USD.market_cap)}
                </span> */}
              <span className="text-sm text-end text-zinc-400">
                {formatCurrency(item.quote.USD.price)}
              </span>
              <span
                className={`text-sm text-end ${
                  item.quote.USD.percent_change_7d < 0
                    ? "text-red-500"
                    : "text-green-500"
                } `}
              >
                {item.quote.USD.percent_change_7d
                  .toFixed(0)
                  .toString()
                  .replace(".", ",")}{" "}
                %
              </span>
              <span
                className={`text-sm text-end ${
                  item.quote.USD.percent_change_30d < 0
                    ? "text-red-500"
                    : "text-green-500"
                } `}
              >
                {item.quote.USD.percent_change_30d
                  .toFixed(0)
                  .toString()
                  .replace(".", ",")}{" "}
                %
              </span>
              <span
                className={`text-sm text-end ${
                  item.quote.USD.percent_change_90d < 0
                    ? "text-red-500"
                    : "text-green-500"
                } `}
              >
                {item.quote.USD.percent_change_90d
                  .toFixed(0)
                  .toString()
                  .replace(".", ",")}{" "}
                %
              </span>
            </div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
};

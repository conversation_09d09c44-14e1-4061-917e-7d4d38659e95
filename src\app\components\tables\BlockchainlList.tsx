"use client";
import {
  Progress,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { useMemo, useState } from "react";
import { ICoingeckoCoinsData } from "../../interfaces/coingecko";
import {
  IBlockchainData,
  ICombinedTVlFeesInterfaces,
  IProtocol,
  ITvlData,
} from "../../interfaces/defillama";
import { formatMarketCap } from "../../utils/format-numbers/formatMarketCap";

export default function BlockchainList({
  blockchainData,
  coingeckoCoinsData,
}: {
  blockchainData: IBlockchainData;
  coingeckoCoinsData: ICoingeckoCoinsData[];
}): React.ReactNode {
  const sortedBlockchainTvl = blockchainData?.tvl
    .slice(0)
    .sort((a: ITvlData, b: ITvlData) => {
      return b.tvl - a.tvl;
    });

  const filteredBlockchainFees = blockchainData?.fees.protocols
    .slice(0)
    .sort((a: IProtocol, b: IProtocol) => {
      return b.total1y - a.total1y;
    })
    .filter((protocol: IProtocol) => protocol.category === "Chain");

  const filteredBlockchainTvlFees: ICombinedTVlFeesInterfaces[] =
    sortedBlockchainTvl?.map((item: ITvlData) => {
      const matchingProtocol = filteredBlockchainFees?.find(
        (protocol: IProtocol) =>
          protocol.slug.toLowerCase() === item.name.toLowerCase()
      );

      return {
        ...item,
        total1y: matchingProtocol?.total1y ?? 0,
        total30d: matchingProtocol?.total30d ?? 0,
        total7d: matchingProtocol?.total7d ?? 0,
      };
    }) || [];

  // console.log("sortedBlockchainTvl with FEES", filteredBlockchainFees);
  // console.log("coinmarketcapData blockchain", coinmarketcapData);
  // console.log("render BlockchainList, isloading", isLoading);

  interface BlockchainTvlTableProps {
    filteredBlockchainTvl: ICombinedTVlFeesInterfaces[];
  }

  const BlokchainTvlTabel: React.FC<BlockchainTvlTableProps> = ({
    filteredBlockchainTvl,
  }) => {
    const columns = [
      { uid: "position", name: "#" },
      { uid: "image", name: "" },
      { uid: "name", name: "Blockchain" },
      { uid: "tvl", name: "TVL" },
      { uid: "tvlPercent", name: "TVL %" },
      { uid: "fees1y", name: "Utile 1a" },
      { uid: "fees30d", name: "Utile 30g" },
    ];

    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [page, setPage] = useState(1);
    const pages = Math.ceil(filteredBlockchainTvl.length / rowsPerPage);
    const items = useMemo(() => {
      const start = (page - 1) * rowsPerPage;
      const end = start + rowsPerPage;

      return filteredBlockchainTvl.slice(start, end);
    }, [page, filteredBlockchainTvl, rowsPerPage]);

    // console.log("filteredBlockchainTvl", filteredBlockchainTvl);

    const renderCell = (
      item: ICombinedTVlFeesInterfaces,
      columnKey: keyof any | string
    ): React.ReactNode => {
      let imageUrl = coingeckoCoinsData?.find(
        (coin: ICoingeckoCoinsData) =>
          coin.id.toLowerCase() ===
          (item.gecko_id?.toLowerCase() || item.name?.toLowerCase())
      )?.image;

      let symbol = coingeckoCoinsData?.find(
        (coin: ICoingeckoCoinsData) =>
          coin.id.toLowerCase() ===
          (item.gecko_id?.toLowerCase() || item.name?.toLowerCase())
      )?.symbol;

      if (item.name?.toLowerCase() === "base") {
        imageUrl = coingeckoCoinsData?.find(
          (coin: ICoingeckoCoinsData) =>
            coin.id.toLowerCase() ===
            (item.gecko_id?.toLowerCase() || "ethereum")
        )?.image;
      }

      switch (columnKey) {
        case "position":
          return (
            <div className="text-zinc-500">
              {filteredBlockchainTvl.indexOf(item) + 1}
            </div>
          );

        case "image":
          return (
            <img
              src={imageUrl}
              alt={item.name}
              className="rounded-full h-[26px] w-[26px]"
            ></img>
          );
        case "name":
          return (
            <div className="align-middle">
              <div className="w-100 truncate">
                {item.name.toLowerCase() === "xrp" ? "Ripple" : item.name}
              </div>
              <div className=" text-zinc-500 text-xs">
                {symbol?.toUpperCase()}
              </div>
            </div>
          );

        case "tvl":
          return formatMarketCap(item.tvl);
        case "tvlPercent":
          const tvlPercent = (
            (item.tvl /
              sortedBlockchainTvl!.reduce(
                (sum: any, c: ITvlData) => sum + c.tvl,
                0
              )) *
            100
          ).toFixed(1);
          return (
            <>
              <Progress
                aria-label="Dominanza"
                size="sm"
                value={+tvlPercent}
                label={tvlPercent.replace(".", ",") + "%"}
                // showValueLabel={true}
                classNames={{
                  label: "flex-1",
                }}
              />
            </>
          );
        // return `${(
        //   (item.tvl /
        //     sortedBlockchainTvl!.reduce(
        //       (sum: any, c: ITvlData) => sum + c.tvl,
        //       0
        //     )) *
        //   100
        // )
        //   .toFixed(1)
        //   .replace(".", ",")}%`;
        case "fees1y":
          return item.total1y === 0 ? "-" : formatMarketCap(item.total1y);
        case "fees30d":
          return item.total30d === 0 ? "-" : formatMarketCap(item.total30d);
        default:
          return null;
      }
    };

    // const TopContent = memo(() => {
    //   return (
    //     <h2 className="text-xl font-bold text-center p-2 bg-tableHeaderBg sticky left-0 rounded-t-xl w-100">
    //       Top Blockchain
    //     </h2>
    //   );
    // });

    // const BottomContent = memo(() => {
    //   return (
    //     <div className="py-2  flex justify-center items-center sticky left-0">
    //       <Pagination
    //         showControls
    //         size="sm"
    //         classNames={{
    //           cursor: "bg-foreground text-background",
    //         }}
    //         color="default"
    //         page={page}
    //         total={pages}
    //         variant="light"
    //         onChange={setPage}
    //       />
    //     </div>
    //   );
    // });

    const classNames = useMemo(
      () => ({
        wrapper: [
          // "max-h-[600px]",
          "p-0",
          "border-0",
          "overflow-x-auto",
          "overflow-y-hidden",
          // "bg-black",
        ],
        table: [],
        th: [
          "bg-transparent",
          "text-default-500",
          "border-b",
          "border-divider",
        ],
        td: [
          // changing the rows border radius
          // first
          "group-data-[first=true]/tr:first:before:rounded-none",
          "group-data-[first=true]/tr:last:before:rounded-none",
          // middle
          "group-data-[middle=true]/tr:before:rounded-none",
          // last
          "group-data-[last=true]/tr:first:before:rounded-none",
          "group-data-[last=true]/tr:last:before:rounded-none",
        ],
      }),
      []
    );

    return (
      <div className="border-1 border-zinc-800 rounded-xl p-0 m-2 relative overflow-hidden tablet:[margin-right:-20px]">
        {/* <TopContent></TopContent> */}
        <div className="overflow-x-auto tablet:[width:calc(100%-20px)]">
          <Table
            removeWrapper
            title="Top Blockchain"
            aria-label="Tabella delle blockchain"
            className="text-white w-full table-auto p-0 m-0"
            classNames={classNames}
          >
            <TableHeader columns={columns}>
              {(column) => (
                <TableColumn
                  key={column.uid}
                  align="start"
                  className={` whitespace-nowrap  ${
                    column.uid === "fees30d"
                      ? "mr-0 text-end"
                      : column.uid === "tvlPercent"
                      ? "mr-0 text-end pl-6"
                      : column.uid === "position"
                      ? "min-w-[32px] px-2 text-end"
                      : column.uid === "image"
                      ? "min-w-[42px] max-w-[42px] p-2"
                      : column.uid === "name"
                      ? "sticky left-0 bg-tableBg p-2 z-10 min-w-[90px] max-w-[90px] pl-2"
                      : "text-end min-w-[100px] max-w-[120px] p-2"
                  } `}
                >
                  {column.name}
                </TableColumn>
              )}
            </TableHeader>
            <TableBody items={items}>
              {(item: ICombinedTVlFeesInterfaces) => (
                <TableRow key={item.name}>
                  {(columnKey) => (
                    <TableCell
                      className={`border-b border-zinc-800
                     ${
                       columnKey === "fees30d"
                         ? "mr-[8px] text-end min-w-[100px] max-w-[100px]"
                         : columnKey === "tvlPercent"
                         ? "text-end pl-6 min-w-[100px] max-w-[120px]"
                         : columnKey === "position"
                         ? "min-w-[32px] px-2 text-end"
                         : columnKey === "image"
                         ? "min-w-[42px] max-w-[42px] p-2 pr-0"
                         : columnKey === "name"
                         ? "sticky left-0 z-10 bg-tableBg min-w-[90px] max-w-[90px] p-2"
                         : "text-end px-2 min-w-[100px] max-w-[120px]"
                     }`}
                      // style={
                      //   columnKey === "name"
                      //     ? {
                      //         boxShadow: "2px 0 2px -1px #e5e7eb",
                      //       }
                      //     : undefined
                      // }
                    >
                      {renderCell(item, columnKey.toString())}
                    </TableCell>
                  )}
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {/* <BottomContent></BottomContent> */}
      </div>
    );
  };

  return (
    <div className="overflow-hidden">
      <BlokchainTvlTabel
        filteredBlockchainTvl={filteredBlockchainTvlFees!.slice(0, 10)}
      ></BlokchainTvlTabel>
    </div>
  );
}

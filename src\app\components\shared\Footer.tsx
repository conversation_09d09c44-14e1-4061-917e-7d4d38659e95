"use client";
import { usePathname } from "next/navigation";
import pkg from "../../../../package.json";

export default function Footer({ time }: { time: Date }) {
  const pathname = usePathname();
  // Nascondi il menu se siamo su /login
  if (pathname === "/login") {
    return null;
  }

  // Ottieni la data attuale compresa di ora e minuti

  const currentTime = (): string => {
    const now = new Date(time);

    const day = ("0" + now.getDate()).slice(-2);
    const month = ("0" + (now.getMonth() + 1)).slice(-2); // I mesi partono da 0!
    const year = now.getFullYear();
    const hours = ("0" + now.getHours()).slice(-2);
    const minutes = ("0" + now.getMinutes()).slice(-2);

    const formattedDate = `${day}/${month}/${year} - ${hours}:${minutes}`;

    return formattedDate;
  };

  return (
    <footer className="px-2 py-8 border-t border-zinc-800 mb-2">
      <p className="text-xs text-center text-zinc-400 mb-3">
        Tutte le informazioni contenute su questo sito non costituiscono
        consigli e solleciti all'investimento.
      </p>
      <p className="text-xs text-center text-zinc-400  mb-2">
        Aggiornamento: {currentTime()}
      </p>
      <p className="text-sm text-center font-semibold text-zinc-300">
        © 2025 Elite Cripto{" "}
        <span className="text-zinc-400 text-sm"> - {pkg.version}</span>
      </p>
    </footer>
  );
}

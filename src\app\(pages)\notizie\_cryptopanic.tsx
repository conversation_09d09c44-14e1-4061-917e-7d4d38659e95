// export default function Notizie() {
//   const { cryptopanicNews } = useCryptoData();

//   if (!cryptopanicNews) {
//     return (
//       <div className="min-h-[100dvh] m-4 flex justify-center items-middle h-100">
//         Caricamento...
//       </div>
//     );
//   }

//   const ListboxWrapper = ({ children }: { children: React.ReactNode }) => (
//     <div className="w-4xl">{children}</div>
//   );

//   return (
//     <>
//       <div className=" w-full">
//         <ListboxWrapper>
//           <Listbox className="w-100">
//             {criptovalutaNews.map((item: ICryptoPanicNews, index) => (
//               <ListboxItem
//                 key={index}
//                 value={item.id}
//                 classNames={{
//                   base: "mb-4 border-b border-zinc-800 rounded-none",
//                   title: "w-100 text-clip pb-2",
//                 }}
//               >
//                 <div className=" grid grid-cols-[60px_auto] grid-rows-1">
//                   <div className="col-[1/2] row-[1/3] self-center justify-center text-end text-xs text-zinc-500 pr-4">
//                     {timeFromNow(item.created_at)}
//                   </div>
//                   <div className="mb-2 col-[2/3] row-[1/2]">
//                     <div className="mb-1 w-100 overflow-y-hidden ">
//                       {item.currencies &&
//                         item.currencies.map(
//                           (c, i) =>
//                             c.code.length > 1 && (
//                               <span className="mr-2 mb-2 text-sky-400" key={i}>
//                                 {c.code}
//                               </span>
//                             )
//                         )}
//                     </div>
//                     <div className="col-[2/3] row-[2/2] text-base/6">
//                       {item.title}
//                     </div>
//                   </div>
//                 </div>
//               </ListboxItem>
//             ))}
//           </Listbox>
//         </ListboxWrapper>
//       </div>
//     </>
//   );
// }

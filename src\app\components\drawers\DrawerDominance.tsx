import {
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DrawerHeader,
} from "@heroui/react";
import Image from "next/image";
import { memo, useEffect, useRef } from "react";

export default function DrawerDominance({
  isDrawerOpen,
  onClose,
}: {
  isDrawerOpen: boolean;
  onClose: () => void;
}) {
  return (
    <>
      <Drawer
        isOpen={isDrawerOpen}
        onOpenChange={onClose}
        placement="bottom"
        className="h-[550px]"
      >
        <DrawerContent>
          {(onClose) => (
            <>
              <DrawerHeader className="flex justify-center">
                <div className="w-100  flex flex-row justify-center items-center">
                  <span className="text-md"> Dominanza</span>
                  <Image
                    src="/btc.png"
                    alt="BTC"
                    className="ml-2"
                    width={20}
                    height={20}
                  ></Image>
                </div>
              </DrawerHeader>
              <DrawerBody className="h-[500px] p-2">
                <TradingViewChart></TradingViewChart>
              </DrawerBody>
              <DrawerFooter>
                {/* <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
                <Button color="primary" onPress={onClose}>
                  Action
                </Button> */}
              </DrawerFooter>
            </>
          )}
        </DrawerContent>
      </Drawer>
    </>
  );
}

const TradingViewChart = memo(() => {
  const container = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const script = document.createElement("script");
    script.src =
      "https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js";
    script.type = "text/javascript";
    script.async = true;
    script.innerHTML = `
        {
          "autosize": true,
          "symbol": "CRYPTOCAP:BTC.D",
          "interval": "D",
          "timezone": "Europe/Rome",
          "theme": "dark",
          "style": "3",
          "locale": "it",
          "hide_top_toolbar": true,
          "hide_legend": true,
          "allow_symbol_change": false,
          "calendar": false,
          "hide_volume": true,
          "support_host": "https://www.tradingview.com"
        }`;
    container!.current!.appendChild(script);
  }, []);

  return (
    <div
      className="tradingview-widget-container rounded-2xl"
      ref={container}
      style={{ height: "100%", width: "100%" }}
    >
      <div
        className="tradingview-widget-container__widget"
        style={{ height: "calc(100% - 12px)", width: "100%" }}
      ></div>
      <div className="tradingview-widget-copyright">
        <a rel="noopener nofollow" target="_blank">
          {/* <span className="blue-text">
            Segui tutti i mercati su TradingView
          </span> */}
        </a>
      </div>
    </div>
  );
});

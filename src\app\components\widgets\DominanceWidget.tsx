"use client";
import { ICoinmarketcapCoinsData } from "@/app/interfaces/coinmarketcap";
import { Card, CardBody } from "@heroui/react";
import Image from "next/image";
import React, { useMemo, useState } from "react";
import DrawerDominance from "../drawers/DrawerDominance";
import { IconArrowRight } from "../shared/Icons";

export const DominanceWidget = ({
  coinmarketcapCoinsData,
}: {
  coinmarketcapCoinsData: ICoinmarketcapCoinsData[];
}): React.ReactNode => {
  // console.log("Rendering DominanceWidget");

  const [drawerDominanceIsOpen, setDrawerDominanceIsOpen] = useState(false);

  const { dominanceBtc, dominanceEth } = useMemo(() => {
    // console.log("Calculating dominance values");

    let btc = 0;
    let eth = 0;

    coinmarketcapCoinsData?.forEach((item) => {
      if (item.slug === "bitcoin") {
        btc = item.quote.USD.market_cap_dominance!;
      } else if (item.slug === "ethereum") {
        eth = item.quote.USD.market_cap_dominance!;
      }
    });

    return { dominanceBtc: btc, dominanceEth: eth };
  }, [coinmarketcapCoinsData]);

  return (
    <>
      {drawerDominanceIsOpen && (
        <DrawerDominance
          isDrawerOpen={drawerDominanceIsOpen}
          onClose={() => setDrawerDominanceIsOpen(false)}
        ></DrawerDominance>
      )}
      <Card
        classNames={{
          base: "rounded-xl",
        }}
      >
        <CardBody
          className="text-center p-3 "
          onClick={() => setDrawerDominanceIsOpen(true)}
        >
          <div className="flex curosor-pointer">
            <span className="text-sm text-zinc-400 font-bold ">
              Dominanza Mercato
            </span>
            <IconArrowRight classNames="ml-2"></IconArrowRight>
          </div>

          <div className="flex flex-col">
            <div className="flex items-center mt-1">
              <Image
                src="/btc.png"
                alt="BTC"
                className="mr-2"
                width={20}
                height={20}
              />
              <span className="font-bold text-md">
                {dominanceBtc.toFixed(1).toString().replace(".", ",")} %
              </span>
            </div>
            <div className="flex items-center">
              <Image
                src="/eth.png"
                alt="ETH"
                className="mr-2"
                width={20}
                height={20}
              />

              <span className="font-bold text-md">
                {dominanceEth.toFixed(1).toString().replace(".", ",")} %
              </span>
            </div>
          </div>

          {/* <div className="flex justify-end">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 512 512"
              height={15}
              width={15}
              className="fill-blue-500"
            >
              <path d="M64 64c0-17.7-14.3-32-32-32S0 46.3 0 64L0 400c0 44.2 35.8 80 80 80l400 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 416c-8.8 0-16-7.2-16-16L64 64zm406.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L320 210.7l-57.4-57.4c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L240 221.3l57.4 57.4c12.5 12.5 32.8 12.5 45.3 0l128-128z" />
            </svg>
          </div> */}
        </CardBody>
      </Card>
    </>
  );
};

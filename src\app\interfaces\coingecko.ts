export interface ICoingeckoCoinsData {
  id: string;
  symbol: string;
  name: string;
  image: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number | null;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number | null;
  max_supply: number | null;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  roi: null | {
    times: number;
    currency: string;
    percentage: number;
  };
  last_updated: string;
  market_cap_dominance?: number;
}

export interface ICoingeckoCategoriesData {
  id: number;
  name: string;
  market_cap: number;
  market_cap_change_24h: number;
  content: string;
  top_3_coins_id: string[];
  top_3_coins: string[];
  volume_24h: number;
  updated_at: string;
}

export interface ICoingeckoCompanyHoldersData {
  total_holdings: number; // Total BTC/ETH holdings of companies
  total_value_usd: number; // Total BTC/ETH holdings value in USD
  market_cap_dominance: number; // Market cap dominance
  companies: ICoingeckoCompanyData[]; // Array of companies
}

export interface ICoingeckoCompanyData {
  name: string; // Company name
  symbol: string; // Company stock symbol
  country: string; // Country where the company is incorporated
  total_holdings: number; // Total BTC/ETH holdings of the company
  total_entry_value_usd: number; // Total entry value in USD
  total_current_value_usd: number; // Total current value of BTC/ETH holdings in USD
  percentage_of_total_supply: number; // Percentage of total BTC/ETH supply
}

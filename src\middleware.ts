import { getToken } from "next-auth/jwt";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

const protectedPages = [
  "/",
  "/dashboard",
  "/notizie",
  "/cripto",
  "/categorie",
  "/blockchain",
  "/token",
  "/volumi",
];

export async function middleware(req: NextRequest) {
  const token = await getToken({ req });
  const { pathname } = req.nextUrl;

  // Protegge le rotte non-API specificate esattamente
  if (!token && protectedPages.includes(pathname)) {
    return NextResponse.redirect(new URL("/login", req.url));
  }

  // Se l'utente è autenticato, reindirizza /login alla home
  if (token && pathname === "/login") {
    return NextResponse.redirect(new URL("/", req.url));
  }

  if (pathname === "/") {
    return NextResponse.redirect(new URL("/dashboard", req.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: "/:path*",
};

// ALTRO MODO
// import { withAuth } from "next-auth/middleware";

// export default withAuth({
//   pages: {
//     signIn: "/login", // Percorso della pagina di login
//   },
//   callbacks: {
//     authorized: ({ token }) => {
//       console.log("Middleware - token:", token); // Log del token
//       return !!token; // Permette l'accesso solo se il token esiste
//     },
//   },

// });

// export const config = {
//   matcher: ["/", "/notizie"] // Percorsi protetti
// };

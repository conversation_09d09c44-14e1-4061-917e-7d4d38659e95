import {
  IBlockchainData,
  IFeesData,
  ITvlData,
} from "@/app/interfaces/defillama";

const DEFILLAMA_TVL_URL = "https://api.llama.fi/v2/chains";
const DEFILLAMA_FEES_URL = "https://api.llama.fi/overview/fees";

export async function fetchBlockchainData(): Promise<IBlockchainData> {
  console.log("Effettuando una nuova chiamata a Defillama - (Tvl + Fees)...");
  try {
    // Esegui la prima chiamata
    const tvlRes = await fetch(DEFILLAMA_TVL_URL);

    // Verifica la risposta della prima chiamata
    if (!tvlRes.ok) {
      const errorDetail = await tvlRes.text();
      throw new Error(`Errore Defillama Blokchain Tvl: ${errorDetail}`);
    }

    // Converte la prima risposta in JSON
    const tvl: ITvlData[] = await tvlRes.json();

    // Esegui la seconda chiamata solo dopo la prima
    const feesRes = await fetch(DEFILLAMA_FEES_URL);

    // Verifica la risposta della seconda chiamata
    if (!feesRes.ok) {
      const errorDetail = await feesRes.text();
      throw new Error(`Errore Defillama Blokchain Fees: ${errorDetail}`);
    }

    // Converte la seconda risposta in JSON
    const fees: IFeesData = await feesRes.json();

    return { tvl, fees } as IBlockchainData;
  } catch (error) {
    console.error(
      "Errore durante il fetch sequenziale dei dati da Defillama (Tvl + Fees):",
      error
    );
    throw new Error(
      "Errore durante il recupero dei dati Defillama (Tvl + Fees)"
    );
  }
}

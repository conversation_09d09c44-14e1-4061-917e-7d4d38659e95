"use client";
import { ICriptovalutaNews } from "@/app/interfaces/criptovalutaNews";
import { timeFromNow } from "@/app/utils/dates/timeFromNow";

export const NewsWidget = ({
  criptovalutaNews,
}: {
  criptovalutaNews: ICriptovalutaNews[];
}): React.ReactNode => {
  return (
    <div
      className="px-2 mx-2 "
      style={
        {
          // backgroundColor: "hsl(240 5.88% 10%)",
        }
      }
    >
      {criptovalutaNews
        ?.slice(0, 1)
        .map((item: ICriptovalutaNews, index, array) => (
          <div
            key={index}
            className={`py-2 ${
              index === array.length - 1 ? "" : "border-b border-zinc-800"
            }`}
          >
            {/* <div className="flex flex-row justify-between">
                <h3 className="text-xs text-blue-400">News</h3>
                <IconArrowRight classNames="ml-2"></IconArrowRight>
              </div> */}

            <h3 className="text-sm mt-2 font-semibold">{item.title}</h3>
            <p className="text-xs text-zinc-400 mt-1">
              Notizia - {timeFromNow(item.pubDate[0]) + " fa"}
            </p>
          </div>
        ))}
    </div>
  );
};

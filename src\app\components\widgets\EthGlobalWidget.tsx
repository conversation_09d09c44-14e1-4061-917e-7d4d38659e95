"use client";
import {
  ICoingeckoCoinsData,
  ICoingeckoCompanyHoldersData,
} from "@/app/interfaces/coingecko";
import { IBtcEtfHistory, IEthEtfHistory } from "@/app/interfaces/googleSheet";
import { formatMarketCap } from "@/app/utils/format-numbers/formatMarketCap";
import { Card, CardBody, Skeleton } from "@heroui/react";
import React from "react";
import { GlobeIcoin } from "../shared/Icons";

export const EthGlobalWidget = ({
  etfHistory,
  coingeckoCompanyHoldersData,
  coingeckoCoinsData,
}: {
  etfHistory:
    | {
        btcEtfHistory: IBtcEtfHistory[];
        ethEtfHistory: IEthEtfHistory[];
      }
    | undefined;
  coingeckoCompanyHoldersData: ICoingeckoCompanyHoldersData;
  coingeckoCoinsData: ICoingeckoCoinsData[];
}): React.ReactNode => {
  if (!etfHistory)
    return <Skeleton className="min-h-[200px] rounded-2xl"></Skeleton>;

  const ethCurrentPrice = coingeckoCoinsData.find(
    (coin) => coin.id === "ethereum"
  )!.current_price;
  const ethHistory = etfHistory.ethEtfHistory.reverse();
  const ethData: {
    category: string;
    total_holdings: number;
    total_value_usd: number;
  }[] = [
    {
      category: "Aziende Pubbliche",
      total_holdings: coingeckoCompanyHoldersData?.total_holdings,
      total_value_usd: coingeckoCompanyHoldersData?.total_value_usd,
    },
    {
      category: "ETF",
      total_holdings: ethHistory[0].totalEth! + 2867000,
      total_value_usd:
        ethHistory[0].total$! * 1000000 + 2867000 * ethCurrentPrice, // ETF inflow + precedenti holdings (2867000 ETH)
    },
  ];

  const summary = {
    total_holdings: ethData[0].total_holdings! + ethData[1].total_holdings!,
    total_value_usd: ethData[0].total_value_usd! + ethData[1].total_value_usd!,
  };

  // console.log("ethData", ethData, "summary eth", summary);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("it-IT").format(num);
  };

  return (
    <Card>
      <CardBody className="p-3">
        {/* <div className="flex justify-between items-center">
            <span className="text-xs text-zinc-400 font-bold">
              Istituzioni in possesso di BTC
            </span>
          </div> */}
        <div className="grid grid-cols-[135px_100px_auto] gap-2 w-100 font-semibold border-b border-zinc-800 pb-1 text-zinc-400">
          <div className="text-sm flex text-blue-500">
            <GlobeIcoin className="mr-2" fill="#3B82F6"></GlobeIcoin>
            Ethereum
          </div>
          <div className="text-sm text-end">Tot. ETH</div>
          <div className="text-sm text-end">Tot. $</div>
        </div>
        <div className="flex flex-col justify-start w-100  border-b border-zinc-800 mb-1  text-zinc-500 font-semibold">
          {ethData
            ?.sort(
              (item1, item2) => item2.total_holdings! - item1.total_holdings!
            )
            .map((item, index, array) => (
              <div
                key={index}
                className={` grid grid-cols-[135px_100px_auto] mt-1 gap-2 pb-1 `}
              >
                <div className=" text-sm">{item.category!}</div>
                <div className="text-sm text-end">
                  {formatNumber(+item.total_holdings!.toFixed(0))}
                </div>
                <div className="text-sm text-end">
                  {formatMarketCap(+item.total_value_usd!)}
                </div>
              </div>
            ))}
        </div>

        <div className="grid grid-cols-[135px_100px_auto] mt-1 gap-2 w-100 mt-1 font-semibold">
          <div className="text-sm">Totale</div>
          <div className="text-sm text-end">
            <span className="text-blue-500 text-sm mr-1">Ξ</span>
            {formatNumber(+summary.total_holdings!.toFixed(0))}
          </div>
          <div className="text-sm text-end">
            <span className="text-sm mr-1">$</span>
            {formatMarketCap(+summary?.total_value_usd!)}
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

"use client";
import { ICoinmarketcapCoinsData } from "@/app/interfaces/coinmarketcap";
import { Card, CardBody, Progress } from "@heroui/react";
import { useMemo, useState } from "react";
import DrawerAltcoin from "../drawers/DrawerAltcoin";
import { IconArrowRight } from "../shared/Icons";

export const AltcoinSeasonIndexWidget = ({
  coinmarketcapCoinsData,
}: {
  coinmarketcapCoinsData: ICoinmarketcapCoinsData[];
}) => {
  const [drawerAltcoinIsOpen, setDrawerAltcoinIsOpen] = useState(false);

  const { altcoinSeasonIndex } = useMemo(() => {
    // console.log("Calculating altcoin season index");

    const btc90d = coinmarketcapCoinsData?.find(
      (item) => item.slug === "bitcoin"
    )?.quote.USD.percent_change_90d!;

    const altcoinSeasonIndex =
      (coinmarketcapCoinsData
        .sort((a, b) => a.cmc_rank - b.cmc_rank)
        .slice(1, 50)
        .filter((item) => item.quote.USD.percent_change_90d! > btc90d).length /
        coinmarketcapCoinsData.slice(1, 50).length) *
      100;

    return { altcoinSeasonIndex };
  }, [coinmarketcapCoinsData]);

  return (
    <>
      {drawerAltcoinIsOpen && (
        <DrawerAltcoin
          coinmarketcapCoinsData={coinmarketcapCoinsData}
          isDrawerOpen={drawerAltcoinIsOpen}
          onClose={() => setDrawerAltcoinIsOpen(false)}
        ></DrawerAltcoin>
      )}
      <Card
        classNames={{
          base: "rounded-xl",
        }}
      >
        <CardBody
          className="text-center p-3"
          onClick={() => setDrawerAltcoinIsOpen(true)}
        >
          <div className="flex">
            <span className="text-sm text-zinc-400 font-bold">
              Altcoin Index
            </span>
            <IconArrowRight classNames="ml-2"></IconArrowRight>
          </div>
          <div className="flex items-center justify-center mt-1">
            <span className="font-bold text-xl mr-1">
              {altcoinSeasonIndex.toFixed(0)}
            </span>
            <span className="text-zinc-500"> / 100 </span>
          </div>
          <div className="relative w-100">
            <Progress
              classNames={{
                base: "w-100 mt-4 rounded-full relative rounded-full border border-gray-700 bg-gradient-to-r from-orange-500 to-blue-600", // Bordi arrotondati per la barra completa
                track: "bg-transparent", // Sfondo scuro per il track
                indicator: "bg-transparent", // Gradiente sull'indicatore
                label: "tracking-wider font-medium text-gray-600",
                value: "text-gray-400",
              }}
              // label="Lose weight"
              radius="sm"
              showValueLabel={false}
              size="sm"
              value={altcoinSeasonIndex}
            />
            {/* Mark sul punto esatto */}
            <div
              className="absolute top-1/2 w-3 h-3 rounded-full bg-white border-2 border-gray-700 shadow-md "
              style={{
                left: `calc(${altcoinSeasonIndex}% - 6px)`, // Posizionamento dinamico
                top: 13,
              }}
            />
            <span
              className="absolute top-1/2 text-xs text-zinc-400 "
              style={{
                left: 0,
                top: -2,
              }}
            >
              Bitcoin
            </span>
            <span
              className="absolute top-1/2 text-xs text-zinc-400 "
              style={{
                right: 0,
                top: -2,
              }}
            >
              Altcoin
            </span>
          </div>
        </CardBody>
      </Card>
    </>
  );
};

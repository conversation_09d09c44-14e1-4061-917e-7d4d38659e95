/**
 * <PERSON><PERSON> l'intervallo di tempo (start_time ed end_time) per gli ultimi "days" giorni.
 * @param days - Numero di giorni da considerare (es. 7 per gli ultimi 7 giorni).
 * @returns Un oggetto contenente start_time ed end_time in formato ISO8601.
 */
export function getTimeRangeForLastDays(days: number): {
  start_time: string;
  end_time: string;
} {
  const now = new Date();
  // Sottrai 15 secondi per garantire che end_time sia almeno 10 secondi prima del momento della richiesta
  const adjustedNow = new Date(now.getTime() - 15000);
  const end_time = adjustedNow.toISOString();

  const start = new Date(now.getTime());
  start.setDate(now.getDate() - days);
  const start_time = start.toISOString();

  return { start_time, end_time };
}

"use client";

import {
  EyeFilledIcon,
  EyeSlashFilledIcon,
} from "@/app/components/shared/Icons";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardFooter,
  CardHeader,
  Divider,
  Image,
  Input,
} from "@heroui/react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

export default function Login() {
  const [code, setCode] = useState<string | undefined>(undefined);
  const [error, setError] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const router = useRouter();
  const input = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (input?.current?.value) {
      setCode(input.current.value);
    }
  }, []);

  const toggleVisibility = () => setIsVisible(!isVisible);

  const handleSubmit = async () => {
    setIsLoading(true);
    setError(undefined);
    setIsVisible(false);
    const result = await signIn("credentials", {
      code,
      redirect: false, // Evitiamo il redirect automatico
    });

    if (result?.error) {
      setError("Codice non valido, riprova.");
      setIsLoading(false);
      setTimeout(() => {
        setError(undefined);
      }, 5000);
    } else {
      // Reindirizza dopo l'accesso riuscito
      router.push("/");
    }
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(undefined);
    setCode(e.target.value);
  };

  return (
    <div className="flex justify-center items-center  h-[var(--login-page-height)] p-6">
      <Card className="max-w-[400px] min-h-[400px]">
        <CardHeader className="flex gap-3">
          <Image
            alt="heroui logo"
            height={40}
            radius="sm"
            src="/logo.png"
            width={40}
          />
          <div className="flex flex-col">
            <p className="text-2xl">Elite Cripto</p>
            <p className="text-small text-default-500">Dashbaord avanzata</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody className="mt-[24px] mb-[0] flex flex-col jusity-center ">
          <p>
            Per poter accedere alla dashboard è necessario un codice d'invito
          </p>

          <Input
            ref={input}
            placeholder="Inserisci qui il tuo codice"
            type={isVisible ? "text" : "password"}
            className="mt-8"
            isDisabled={isLoading}
            value={code}
            onChange={(e) => onChange(e)}
            onKeyDown={(e) => e.key === "Enter" && handleSubmit()}
            endContent={
              <button
                aria-label="visualizza password"
                className="focus:outline-none"
                type="button"
                onClick={toggleVisibility}
              >
                {isVisible ? (
                  <EyeSlashFilledIcon className="text-xl text-default-400 pointer-events-none" />
                ) : (
                  <EyeFilledIcon className="text-xl text-default-400 pointer-events-none" />
                )}
              </button>
            }
          />
          <div className="flex justify-center flex-col mt-[20px]">
            <Button
              className="mt-4 bg-orange-400"
              isDisabled={!code || isLoading}
              onPress={handleSubmit}
              isLoading={isLoading}
            >
              {isLoading ? null : "Accedi"}
            </Button>
          </div>

          {error && (
            <p
              style={{ color: "red" }}
              className="mt-4 mb-0 text-sm text-center"
            >
              {error}
            </p>
          )}
        </CardBody>
        <Divider />
        <CardFooter className="flex justify-center ">
          <p className="text-xs text-default-500 ">
            Non sono consigli d'investimento
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}

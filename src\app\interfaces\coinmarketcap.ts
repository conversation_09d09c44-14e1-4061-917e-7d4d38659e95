export interface ICoinmarketcapCoinsData {
  id: number; // The unique CoinMarketCap ID for this cryptocurrency.
  name: string; // The name of this cryptocurrency.
  symbol: string; // The ticker symbol for this cryptocurrency.
  slug: string; // The web URL friendly shorthand version of this cryptocurrency name.
  cmc_rank: number; // The cryptocurrency's CoinMarketCap rank by market cap.
  num_market_pairs: number; // The number of active trading pairs available for this cryptocurrency across supported exchanges.
  circulating_supply: number; // The approximate number of coins circulating for this cryptocurrency.
  total_supply: number; // The approximate total amount of coins in existence right now (minus any coins that have been verifiably burned).
  market_cap_by_total_supply?: number; // The market cap by total supply. Optional.
  max_supply: number | null; // The expected maximum limit of coins ever to be available for this cryptocurrency.
  infinite_supply: boolean; // The cryptocurrency is known to have an infinite supply.
  last_updated: string; // Timestamp (ISO 8601) of the last time this cryptocurrency's market data was updated.
  date_added: string; // Timestamp (ISO 8601) of when this cryptocurrency was added to CoinMarketCap.
  tags: string[]; // Array of tags associated with this cryptocurrency.
  self_reported_circulating_supply?: number | null; // The self-reported number of coins circulating for this cryptocurrency.
  self_reported_market_cap?: number | null; // The self-reported market cap for this cryptocurrency.
  tvl_ratio?: number; // Percentage of Total Value Locked. Optional.
  platform?: Platform | null; // Metadata about the parent cryptocurrency platform.
  quote: Quote; // A map of market quotes in different currency conversions
}

interface Platform {
  id: number; // The unique CoinMarketCap ID for the parent platform cryptocurrency.
  name: string; // The name of the parent platform cryptocurrency.
  symbol: string; // The ticker symbol for the parent platform cryptocurrency.
  slug: string; // The web URL friendly shorthand version of the parent platform cryptocurrency name.
  token_address: string; // The token address on the parent platform cryptocurrency.
}

interface Quote {
  [key: string]: CurrencyDetails; // A market quote in the currency conversion option.
}

interface CurrencyDetails {
  price: number; // Price in the specified currency.
  volume_24h: number; // Rolling 24 hour adjusted volume in the specified currency.
  volume_change_24h: number; // 24 hour change in the specified currency's volume.
  volume_24h_reported?: number; // Rolling 24 hour reported volume. Optional.
  volume_7d?: number; // Rolling 7 day adjusted volume. Optional.
  volume_7d_reported?: number; // Rolling 7 day reported volume. Optional.
  volume_30d?: number; // Rolling 30 day adjusted volume. Optional.
  volume_30d_reported?: number; // Rolling 30 day reported volume. Optional.
  market_cap: number; // Market cap in the specified currency.
  market_cap_dominance: number; // Market cap dominance in the specified currency.
  fully_diluted_market_cap: number; // Fully diluted market cap in the specified currency.
  tvl?: number; // Total Value Locked. Optional.
  percent_change_1h: number; // 1 hour change in the specified currency.
  percent_change_24h: number; // 24 hour change in the specified currency.
  percent_change_7d: number; // 7 day change in the specified currency.
  percent_change_30d: number; // 30 day change in the specified currency.
  percent_change_90d: number; // 90 day change in the specified currency.
  last_updated: string; // Timestamp (ISO 8601) of when the conversion currency's current value was referenced.
}

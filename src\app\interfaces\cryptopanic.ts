export interface ICryptoPanicNews {
  kind: string; // e.g., "news"
  domain: string; // e.g., "coinpedia.org"
  title: string; // Title of the news item
  created_at: string; // ISO timestamp
  currencies: Currency[]; // Array of currencies (add details if known)
  id: number; // Unique ID
  published_at: string; // ISO timestamp
  slug: string; // URL-friendly string for the news
  source: Source; // Source object with details
  url: string; // Full URL to the news item
  votes: Votes; // Object representing votes and reactions
}

interface Currency {
  // Define currency properties if available
  [key: string]: any; // Placeholder for unknown structure
}

interface Source {
  title: string; // Title of the source, e.g., "coinpedia"
  region: string; // Region code, e.g., "en"
  domain: string; // Domain of the source, e.g., "coinpedia.org"
  path?: string | null; // Optional path information
  type: string; // Type of source, e.g., "feed"
}

interface Votes {
  negative: number; // Count of negative votes
  positive: number; // Count of positive votes
  important: number; // Count of "important" votes
  liked: number; // Count of "liked" votes
  lol: number; // Count of "lol" reactions
  saved: number; // Count of "saved" reactions
  toxic: number; // Count of "toxic" reactions
  comments: number; // Count of comments
  disliked: number; // Count of "disliked" votes
}

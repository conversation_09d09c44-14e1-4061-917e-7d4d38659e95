{"name": "elite-cripto", "version": "0.2.10", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@heroui/react": "2.6.14", "@supabase/supabase-js": "^2.48.1", "@vercel/analytics": "^1.5.0", "bcrypt": "^5.1.1", "cheerio": "^1.0.0", "framer-motion": "^12.4.1", "next": "^15.4.1", "next-auth": "^4.24.11", "openai": "^4.83.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.1", "xml2js": "^0.6.2", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcrypt": "^5.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/xml2js": "^0.4.14", "eslint": "^9", "eslint-config-next": "^15.4.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}
export interface IBtcEtfHistory {
  date: string;
  daily$: number;
  dailyBtc: number;
  total$: number;
  totalBtc: number;
}
export interface IEthEtfHistory {
  date: string;
  daily$: number;
  dailyEth: number;
  total$: number;
  totalEth: number;
}

export interface ICombinedEtfHistory extends IEthEtfHistory {
  date: string;
  daily$: number;
  dailyBtc: number;
  total$: number;
  totalBtc: number;
}

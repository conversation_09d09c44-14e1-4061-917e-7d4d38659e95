import { createClient } from "@supabase/supabase-js";

// Tipi per i tweet recuperati dalla API di Twitter
export type TweetType = {
  id: string;
  username: string;
  text: string;
  created_at: string;
  authorId?: string;
};
export type Database = {
  public: {
    Tables: {
      tweets: {
        Row: TweetType;
        Insert: {
          // Se "id" viene generato dal DB, puoi renderlo opzionale in Insert
          id?: string;
          username: string;
          text: string;
          created_at: string;
        };
      };
    };
  };
};

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

const TWITTER_BEARER_TOKEN = process.env.TWITTER_BEARER_TOKEN!;

/**
 * Recupera i tweet da uno o più account Twitter in un'unica chiamata.
 * Se viene passato un solo username, la query sarà "from:username",
 * altrimenti verranno combinati gli username usando OR.
 * La query include l'espansione per ottenere il campo "username" dall'autore.
 *
 * @param usernames - Array di username (senza il simbolo @)
 * @param start_time - (Opzionale) Timestamp ISO8601 di inizio
 * @param end_time - (Opzionale) Timestamp ISO8601 di fine
 * @returns Un array di tweet del tipo TweetType
 */
export async function fetchTweetsForMultipleUsers(
  usernames: string[],
  start_time?: string,
  end_time?: string
): Promise<TweetType[]> {
  // Costruisce la query: se c'è un solo username, usa "from:username",
  // altrimenti combina gli username con OR.
  const query =
    usernames.length === 1
      ? `from:${usernames[0]}`
      : usernames.map((username) => `from:${username}`).join(" OR ");

  // L'endpoint include l'espansione per ottenere l'username dall'autore (tramite author_id)
  let url = `https://api.twitter.com/2/tweets/search/recent?query=${encodeURIComponent(
    query
  )}&tweet.fields=created_at,text,author_id&expansions=author_id&user.fields=username`;

  if (start_time) {
    url += `&start_time=${encodeURIComponent(start_time)}`;
  }
  if (end_time) {
    url += `&end_time=${encodeURIComponent(end_time)}`;
  }

  const res = await fetch(url, {
    headers: { Authorization: `Bearer ${TWITTER_BEARER_TOKEN}` },
  });

  const json = await res.json();
  console.log("FETCHED TWITTER DATA", json);

  const tweets: TweetType[] = [];
  if (json.data && json.includes && json.includes.users) {
    const users = json.includes.users;
    for (const tweet of json.data) {
      // Trova l'utente corrispondente all'autore tramite author_id
      const user = users.find((u: TweetType) => u.id === tweet.author_id);
      tweets.push({
        id: tweet.id,
        text: tweet.text,
        created_at: tweet.created_at,
        username: user ? user.username : "",
      });
    }
  }
  return tweets;
}

/**
 * Recupera i tweet da una lista di account e li inserisce (o aggiorna)
 * nella tabella Supabase "tweets". Utilizza fetchTweetsForMultipleUsers per
 * effettuare una singola chiamata combinata.
 *
 * @param start_time - (Opzionale) Timestamp ISO8601 di inizio
 * @param end_time - (Opzionale) Timestamp ISO8601 di fine
 * @returns Un array dei tweet inseriti/aggiornati
 */
export async function fetchAndStoreTweets(
  start_time?: string,
  end_time?: string
): Promise<TweetType[]> {
  // Definisce gli account Twitter (senza il simbolo @)
  const usernames = ["WatcherGuru", "BitcoinMagazine"];

  // Recupera i tweet in un'unica chiamata combinata
  const tweets = await fetchTweetsForMultipleUsers(
    usernames,
    start_time,
    end_time
  );

  // Inserisce (o aggiorna) i tweet nella tabella Supabase "tweets"
  const { data, error } = await supabase
    .from("tweets")
    .upsert(tweets, { onConflict: "id" });

  if (error) {
    throw new Error(`Error upserting tweets: ${error.message}`);
  }

  return data || [];
}

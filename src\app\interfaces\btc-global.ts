export interface IBtcGlobal {
  summary: IBtcGlobalSummaryData;
  data: IBtcGlobalCategoryData[];
  history: IBtcGlobalHistory[];
  error?: string;
}

export interface IBtcGlobalCategoryData {
  category: string;
  btcHoldings: string;
  valueToday: string;
  percentageOf21m: string;
}

export interface IBtcGlobalSummaryData {
  entities: string;
  btcTotal: string;
  valueToday: string;
  percentageOf21m: string;
  lastUpdated: string;
}

export interface IBtcGlobalHistory {
  date: string;
  btcPrice: number;
  btcTotal: number;
  value: number;
  data: {
    category: string;
    btcHoldings: number;
  }[];
}

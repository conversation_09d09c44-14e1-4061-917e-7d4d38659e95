export interface ICombinedTVlFeesInterfaces extends ITvlData {
  total1y: number;
  total30d: number;
  total7d: number;
}

export interface ITvlData {
  chainId: number;
  cmcId: string;
  gecko_id: string;
  name: string;
  tokenSymbol: string;
  tvl: number;
}

export interface IFeesData {
  allChains: string[];
  protocols: IProtocol[];
  breakdown24h: any;
  chain: any;
  change_1d: number;
  change_1m: number;
  change_30dover30d: number;
  change_7d: number;
  change_7dover7d: number;
  total14dto7d: number;
  total1y: number;
  total24h: number;
  total30DaysAgo: number;
  total30d: number;
  total48hto24h: number;
  total60dto30d: number;
  total7DaysAgo: number;
  total7d: number;
  totalDataChart: any[];
  totalDataChartBreakdown: any[];
}

export interface Breakdown24h {
  [key: string]: number;
}

export interface Methodology {
  UserFees: string;
  Fees: string;
  Revenue: string;
}

export interface IProtocol {
  total14dto7d: number;
  total1y: number;
  total24h: number;
  total30DaysAgo: number;
  total30d: number;
  total48hto24h: number;
  total60dto30d: number;
  total7DaysAgo: number;
  total7d: number;
  totalAllTime: number;
  average1y: number | null;
  breakdown24h: Breakdown24h;
  category: string;
  chains: string[];
  change_1d: number;
  change_1m: number;
  change_30dover30d: number;
  change_7d: number;
  change_7dover7d: number;
  defillamaId: string;
  displayName: string;
  id: string;
  latestFetchIsOk: boolean;
  logo: string;
  methodology: Methodology;
  methodologyURL: string;
  module: string;
  name: string;
  protocolType: string;
  slug: string;
}

export interface IBlockchainData {
  tvl: ITvlData[];
  fees: IFeesData;
}

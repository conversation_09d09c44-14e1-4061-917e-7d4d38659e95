export function formatCurrency(value: number): string {
  if (value > 100) {
    // Se il valore è maggiore di 100, rimuovi i decimali
    const formattedInteger = Math.floor(value)
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    return `${formattedInteger} $`;
  } else {
    // Dividi il valore in parte intera e decimale
    const [integer, decimal] = value.toFixed(2).split(".");

    // Aggiungi il separatore delle migliaia
    const formattedInteger = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ".");

    // Combina con la parte decimale e il simbolo "$"
    return `${formattedInteger},${decimal} $`;
  }
}

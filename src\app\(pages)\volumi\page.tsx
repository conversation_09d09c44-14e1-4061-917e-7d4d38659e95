import CryptoVolumesList from "@/app/components/tables/CryptoVolumesList";
import { fetchCoingeckoCoinsData } from "@/app/services/http/coingeckoService";
import { fetchCoinmarketcapCoinsData } from "@/app/services/http/coinmarketcapService";

export default async function Volumi() {
  const [_coingeckoCoinsData, _coinmarketcapCoinsData] = await Promise.all([
    fetchCoingeckoCoinsData(),
    fetchCoinmarketcapCoinsData(),
  ]);
  return (
    <div className="mb-4">
      <p className="m-3 text-sm text-zinc-500 ">
        I volumi rappresentano un indicatore chiave dell’interesse degli
        investitori, della liquidità di un token o di una moneta e possono
        influenzare la
        <span className="text-sm font-bold text-zinc-300"> volatilità</span> e i
        movimenti di prezzo di un progetto.
      </p>
      <CryptoVolumesList
        coingeckoCoinsData={_coingeckoCoinsData}
        coinmarketcapCoinsData={_coinmarketcapCoinsData}
      ></CryptoVolumesList>
    </div>
  );
}

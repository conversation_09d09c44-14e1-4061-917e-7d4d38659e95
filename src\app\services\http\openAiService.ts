import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

/**
 * Funzione per ottenere un riassunto di un gruppo di articoli
 * @param {string[]} articles - Array di stringhe contenenti i testi degli articoli
 * @returns {Promise<string>} - Riassunto dei testi forniti
 */
async function summarizeArticles(articles: string[]): Promise<string> {
  const combinedText = articles.join("\n\n");
  const prompt = `Fornisci un breve riassunto / analisi in italiano dei seguenti articoli (possibilmente come una lista):\n\n${combinedText}`;

  const response = await openai.completions.create({
    model: "gpt-3.5-turbo",
    prompt: prompt,
    max_tokens: 500, // <PERSON>ita la lunghezza del riassunto
  });

  return response.choices[0].text;
}

/**
 * Funzione principale per gestire la suddivisione e il riassunto degli articoli
 * @param {string[]} allArticles - Array di stringhe contenenti tutti i testi degli articoli
 * @returns {Promise<string>} - Riassunto finale di tutti gli articoli
 */
export async function getFinalSummary(allArticles: string[]): Promise<string> {
  const tokenLimitPerGroup = 3000; // Numero approssimativo di parole per gruppo
  let currentGroup: string[] = [];
  let currentLength = 0;
  const groupSummaries = [];

  for (const article of allArticles) {
    const articleLength = article.split(" ").length;
    if (currentLength + articleLength > tokenLimitPerGroup) {
      // Riassumi il gruppo corrente
      const summary = await summarizeArticles(currentGroup);
      groupSummaries.push(summary);
      // Inizia un nuovo gruppo
      currentGroup = [article];
      currentLength = articleLength;
    } else {
      currentGroup.push(article);
      currentLength += articleLength;
    }
  }

  // Riassumi l'ultimo gruppo
  if (currentGroup.length > 0) {
    const summary = await summarizeArticles(currentGroup);
    groupSummaries.push(summary);
  }

  // Combina tutti i riassunti dei gruppi e ottieni il riassunto finale
  const finalSummary = await summarizeArticles(groupSummaries);
  return finalSummary;
}

// Esempio di utilizzo
// (async () => {
//   const articles = [
//     "Testo del primo articolo...",
//     "Testo del secondo articolo...",
//     // Aggiungi altri articoli secondo necessità
//   ];

//   const summary = await getFinalSummary(articles);
//   console.log("Riassunto Finale:", summary);
// })();

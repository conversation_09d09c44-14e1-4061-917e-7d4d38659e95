"use client";

import { AltcoinSeasonIndexWidget } from "@/app/components/widgets/AltcoinSeasonIndexWidget";
import { DominanceWidget } from "@/app/components/widgets/DominanceWidget";
import { Top3Coins } from "@/app/components/widgets/Top3Coins";
import { TopMarketCapWidget } from "@/app/components/widgets/TopMarketCapWidget";
import {
  useCoingeckoCoins,
  useCoinmarketcapCoins,
} from "@/app/hooks/useApiWithCache";
import {
  EtfWidgetWrapperClient,
  GlobalWidgetWrapperClient,
} from "./ClientWrappers";

export default function DashboardClient() {
  console.log("🔄 Caricamento Dashboard Client...");

  // Utilizzo degli hook con cache
  const {
    data: coingeckoCoinsData,
    loading: coingeckoLoading,
    error: coingeckoError,
  } = useCoingeckoCoins();

  const {
    data: coinmarketcapCoinsData,
    loading: coinmarketcapLoading,
    error: coinmarketcapError,
  } = useCoinmarketcapCoins();

  // Mostra loading se uno dei servizi principali sta caricando
  if (coingeckoLoading || coinmarketcapLoading) {
    return (
      <div className="mb-8 p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
        <p className="text-white">Caricamento dati...</p>
      </div>
    );
  }

  // Mostra errore se ci sono problemi critici
  if (coingeckoError || coinmarketcapError) {
    return (
      <div className="mb-8 p-8 text-center">
        <div className="text-red-500 mb-4">
          ⚠️ Errore nel caricamento dei dati
        </div>
        {coingeckoError && (
          <p className="text-sm text-red-400 mb-2">
            CoinGecko: {coingeckoError.message}
          </p>
        )}
        {coinmarketcapError && (
          <p className="text-sm text-red-400 mb-2">
            CoinMarketCap: {coinmarketcapError.message}
          </p>
        )}
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Riprova
        </button>
      </div>
    );
  }

  // Se non ci sono dati, mostra messaggio
  if (!coingeckoCoinsData || !coinmarketcapCoinsData) {
    return (
      <div className="mb-8 p-8 text-center">
        <p className="text-white">Nessun dato disponibile</p>
      </div>
    );
  }

  // Filtra i dati di CoinMarketCap
  const filteredCoinmarketcapData = coinmarketcapCoinsData
    .slice(0)
    .filter(
      (item: any) =>
        !item.tags.includes("stablecoin") && !item.slug.includes("classic")
    );

  return (
    <div className="mb-8">
      {/* Widgets principali */}
      <div className="mx-3 mt-4 mb-8 grid grid-cols-2 gap-4">
        <DominanceWidget coinmarketcapCoinsData={filteredCoinmarketcapData} />
        <AltcoinSeasonIndexWidget
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        />
      </div>

      <div className="mx-3 my-8">
        <TopMarketCapWidget
          coinmarketcapCoinsData={filteredCoinmarketcapData}
          coingeckoCoinsData={coingeckoCoinsData}
        />
      </div>

      <div className="mx-3 my-8 grid grid-cols-2 gap-4">
        <Top3Coins
          range="7gg"
          coingeckoCoinsData={coingeckoCoinsData}
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        />
        <Top3Coins
          range="30gg"
          coingeckoCoinsData={coingeckoCoinsData}
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        />
      </div>

      {/* Widget ETF */}
      <div className="mx-3 my-8 grid grid-cols-1 gap-4">
        <EtfWidgetWrapperClient />
      </div>

      {/* Widget Global */}
      <GlobalWidgetWrapperClient />
    </div>
  );
}

"use client";

import { AltcoinSeasonIndexWidget } from "@/app/components/widgets/AltcoinSeasonIndexWidget";
import { DominanceWidget } from "@/app/components/widgets/DominanceWidget";
import { Top3Coins } from "@/app/components/widgets/Top3Coins";
import { TopMarketCapWidget } from "@/app/components/widgets/TopMarketCapWidget";
import {
  useCoingeckoCoins,
  useCoinmarketcapCoins,
} from "@/app/hooks/useApiWithCache";
import React, { Suspense } from "react";
import { EtfLoader } from "../../(pages)/dashboard/etf-loader";
import { GlobalLoader } from "../../(pages)/dashboard/global-loader";

const EtfWidgetWrapper = React.lazy(() =>
  import("../../(pages)/dashboard/wrappers").then((mod) => ({
    default: mod.EtfWidgetWrapper,
  }))
);

const GlobalWidgetWrapper = React.lazy(() =>
  import("../../(pages)/dashboard/wrappers").then((mod) => ({
    default: mod.GlobalWidgetWrapper,
  }))
);

export default function DashboardClient() {
  console.log("🔄 Caricamento Dashboard Client...");

  // Utilizzo degli hook con cache
  const {
    data: coingeckoCoinsData,
    loading: coingeckoLoading,
    error: coingeckoError,
  } = useCoingeckoCoins();

  const {
    data: coinmarketcapCoinsData,
    loading: coinmarketcapLoading,
    error: coinmarketcapError,
  } = useCoinmarketcapCoins();

  // Mostra loading se uno dei servizi principali sta caricando
  if (coingeckoLoading || coinmarketcapLoading) {
    return (
      <div className="mb-8 p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
        <p className="text-white">Caricamento dati cripto...</p>
        {coingeckoLoading && (
          <p className="text-sm text-gray-400">• CoinGecko</p>
        )}
        {coinmarketcapLoading && (
          <p className="text-sm text-gray-400">• CoinMarketCap</p>
        )}
      </div>
    );
  }

  // Mostra errore se ci sono problemi critici
  if (coingeckoError || coinmarketcapError) {
    return (
      <div className="mb-8 p-8 text-center">
        <div className="text-red-500 mb-4">
          ⚠️ Errore nel caricamento dei dati
        </div>
        {coingeckoError && (
          <p className="text-sm text-red-400 mb-2">
            CoinGecko: {coingeckoError.message}
          </p>
        )}
        {coinmarketcapError && (
          <p className="text-sm text-red-400 mb-2">
            CoinMarketCap: {coinmarketcapError.message}
          </p>
        )}
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Riprova
        </button>
      </div>
    );
  }

  // Se non ci sono dati, mostra messaggio
  if (!coingeckoCoinsData || !coinmarketcapCoinsData) {
    return (
      <div className="mb-8 p-8 text-center">
        <p className="text-white">Nessun dato disponibile</p>
      </div>
    );
  }

  // Filtra i dati di CoinMarketCap
  const filteredCoinmarketcapData = coinmarketcapCoinsData
    .slice(0)
    .filter(
      (item: any) =>
        !item.tags.includes("stablecoin") && !item.slug.includes("classic")
    );

  return (
    <div className="mb-8">
      {/* Widgets principali */}
      <div className="mx-3 mt-4 mb-8 grid grid-cols-2 gap-4">
        <DominanceWidget coinmarketcapCoinsData={filteredCoinmarketcapData} />
        <AltcoinSeasonIndexWidget
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        />
      </div>

      <div className="mx-3 my-8">
        <TopMarketCapWidget
          coinmarketcapCoinsData={filteredCoinmarketcapData}
          coingeckoCoinsData={coingeckoCoinsData}
        />
      </div>

      <div className="mx-3 my-8 grid grid-cols-2 gap-4">
        <Top3Coins
          range="7gg"
          coingeckoCoinsData={coingeckoCoinsData}
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        />
        <Top3Coins
          range="30gg"
          coingeckoCoinsData={coingeckoCoinsData}
          coinmarketcapCoinsData={filteredCoinmarketcapData}
        />
      </div>

      {/* Widget ETF */}
      <div className="mx-3 my-8 grid grid-cols-2 gap-4">
        <Suspense fallback={<EtfLoader />}>
          <EtfWidgetWrapper />
        </Suspense>
      </div>

      {/* Widget Global */}
      <Suspense fallback={<GlobalLoader />}>
        <GlobalWidgetWrapper />
      </Suspense>

      {/* Debug info - rimuovi in produzione */}
      {process.env.NODE_ENV === "development" && (
        <div className="mx-3 my-8 p-4 bg-gray-800 rounded text-xs">
          <h4 className="text-white font-bold mb-2">Debug Info:</h4>
          <p className="text-gray-300">
            CoinGecko coins: {coingeckoCoinsData?.length || 0}
          </p>
          <p className="text-gray-300">
            CoinMarketCap coins: {coinmarketcapCoinsData?.length || 0}
          </p>
          <p className="text-gray-300">
            Filtered CMC: {filteredCoinmarketcapData?.length || 0}
          </p>
        </div>
      )}
    </div>
  );
}

import * as cheerio from "cheerio";
import { NextResponse } from "next/server";

const BITBO_URL = "https://treasuries.bitbo.io/";

// <PERSON><PERSON> storici (copiati dal servizio originale)
const BTCGLOBAL_HISTORY = [
  {
    date: "2023-01-01",
    btcPrice: 16500,
    btcTotal: 1718123,
    value: 28349029500,
    data: [
      {
        category: "Private Companies",
        btcHoldings: 430291,
      },
      {
        category: "Countries",
        btcHoldings: 100240,
      },
      {
        category: "Defi",
        btcHoldings: 186583,
      },
      {
        category: "ETFs",
        btcHoldings: 794875,
      },
      {
        category: "Public Companies",
        btcHoldings: 206134,
      },
    ],
  },
  {
    date: "2024-01-01",
    btcPrice: 42900,
    btcTotal: 1781691,
    value: 76434543900,
    data: [
      {
        category: "Private Companies",
        btcHoldings: 488270,
      },
      {
        category: "Countries",
        btcHoldings: 90379,
      },
      {
        category: "Defi",
        btcHoldings: 159252,
      },
      {
        category: "ETFs",
        btcHoldings: 771013,
      },
      {
        category: "Public Companies",
        btcHoldings: 272777,
      },
    ],
  },
  {
    date: "2025-01-01",
    btcPrice: 94000,
    btcTotal: 2946957,
    value: 277013958000,
    data: [
      {
        category: "Private Companies",
        btcHoldings: 407201,
      },
      {
        category: "Countries",
        btcHoldings: 513792,
      },
      {
        category: "Defi",
        btcHoldings: 145116,
      },
      {
        category: "ETFs",
        btcHoldings: 1289031,
      },
      {
        category: "Public Companies",
        btcHoldings: 591817,
      },
    ],
  },
];

export async function GET() {
  try {
    console.log("Effettuando una nuova chiamata a Bitbo Treasuries...");

    const response = await fetch(BITBO_URL);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.text();
    const $ = cheerio.load(data);
    const categories: any[] = [];
    let summary: any = null;

    // Estrarre i dati della tabella principale (summary)
    const summaryRow = $(".top-table-data-row");
    if (summaryRow.length) {
      summary = {
        entities: summaryRow.find(".td-symbol").text().trim(),
        btcTotal: summaryRow
          .find(".td-company_btc")
          .text()
          .trim()
          .replace(/,/g, ""),
        valueToday: summaryRow
          .find(".td-value")
          .text()
          .trim()
          .replace(/,/g, ""),
        percentageOf21m: summaryRow.find(".td-company_percent").text().trim(),
        lastUpdated: summaryRow.find(".td-last-updated").text().trim(),
      };
    }

    // Estrarre i dati della tabella "Totals by Category"
    $(".treasuries-table--smaller tbody tr").each((index, element) => {
      const category =
        $(element).find("td.td-symbol a").text().trim() ||
        $(element).find("td.td-symbol").text().trim();
      const btcHoldings = $(element)
        .find("td.td-company_btc")
        .text()
        .trim()
        .replace(/,/g, "");
      const valueToday = $(element)
        .find("td.td-value")
        .text()
        .trim()
        .replace(/,/g, "");
      const percentageOf21m = $(element)
        .find("td.td-company_percent")
        .text()
        .trim();

      // Escludere il primo elemento se erroneamente catturato dalla tabella principale
      if (category && category !== summary?.entities) {
        categories.push({
          category,
          btcHoldings,
          valueToday,
          percentageOf21m,
        });
      }
    });

    const result = {
      summary,
      data: categories,
      history: BTCGLOBAL_HISTORY,
    };

    // Debug: log dei dati che vengono restituiti
    console.log("🔍 API Bitbo - Dati restituiti:", {
      summaryExists: !!result.summary,
      dataLength: result.data?.length,
      historyLength: result.history?.length,
      historyDates: result.history?.map((h) => h.date),
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Errore durante il fetch da Bitbo Treasuries:", error);
    return NextResponse.json(
      { error: "Errore durante il recupero dei dati Bitbo Treasuries" },
      { status: 500 }
    );
  }
}

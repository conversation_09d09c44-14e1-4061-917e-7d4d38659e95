import * as cheerio from "cheerio";
import { NextResponse } from "next/server";

const BITBO_URL = "https://treasuries.bitbo.io/";

// <PERSON>ti storici (dati approssimativi per il widget)
const BTCGLOBAL_HISTORY = [
  {
    date: "2023-01-01",
    btcPrice: 16500,
    btcTotal: 1718123,
    value: 28349029500,
    data: [
      {
        category: "Private Companies",
        btcHoldings: 430291,
        valueToday: 7099801500,
        percentageOf21m: "2.05%",
      },
    ],
  },
  {
    date: "2024-01-01",
    btcPrice: 42000,
    btcTotal: 1850000,
    value: 77700000000,
    data: [
      {
        category: "Private Companies",
        btcHoldings: 450000,
        valueToday: 18900000000,
        percentageOf21m: "2.14%",
      },
    ],
  },
  {
    date: "2025-01-01",
    btcPrice: 95000,
    btcTotal: 1950000,
    value: 185250000000,
    data: [
      {
        category: "Private Companies",
        btcHoldings: 470000,
        valueToday: 44650000000,
        percentageOf21m: "2.24%",
      },
    ],
  },
];

export async function GET() {
  try {
    console.log("Effettuando una nuova chiamata a Bitbo Treasuries...");

    const response = await fetch(BITBO_URL);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.text();
    const $ = cheerio.load(data);
    const categories: any[] = [];
    let summary: any = null;

    // Estrarre i dati della tabella principale (summary)
    const summaryRow = $(".top-table-data-row");
    if (summaryRow.length) {
      summary = {
        entities: summaryRow.find(".td-symbol").text().trim(),
        btcTotal: summaryRow
          .find(".td-company_btc")
          .text()
          .trim()
          .replace(/,/g, ""),
        valueToday: summaryRow
          .find(".td-value")
          .text()
          .trim()
          .replace(/,/g, ""),
        percentageOf21m: summaryRow.find(".td-company_percent").text().trim(),
        lastUpdated: summaryRow.find(".td-last-updated").text().trim(),
      };
    }

    // Estrarre i dati della tabella "Totals by Category"
    $(".treasuries-table--smaller tbody tr").each((index, element) => {
      const category =
        $(element).find("td.td-symbol a").text().trim() ||
        $(element).find("td.td-symbol").text().trim();
      const btcHoldings = $(element)
        .find("td.td-company_btc")
        .text()
        .trim()
        .replace(/,/g, "");
      const valueToday = $(element)
        .find("td.td-value")
        .text()
        .trim()
        .replace(/,/g, "");
      const percentageOf21m = $(element)
        .find("td.td-company_percent")
        .text()
        .trim();

      // Escludere il primo elemento se erroneamente catturato dalla tabella principale
      if (category && category !== summary?.entities) {
        categories.push({
          category,
          btcHoldings,
          valueToday,
          percentageOf21m,
        });
      }
    });

    const result = {
      summary,
      data: categories,
      history: BTCGLOBAL_HISTORY,
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error("Errore durante il fetch da Bitbo Treasuries:", error);
    return NextResponse.json(
      { error: "Errore durante il recupero dei dati Bitbo Treasuries" },
      { status: 500 }
    );
  }
}

export function formatMarketCap(value: number): string {
  // Funzione per aggiungere il separatore delle migliaia
  const formatNumber = (
    num: number,
    includeDecimals: boolean = true
  ): string => {
    const isNegative = num < 0; // Controlla se il numero è negativo
    const absoluteValue = Math.abs(num); // Usa il valore assoluto per la formattazione
    const [integer, decimal] = absoluteValue.toFixed(1).split(".");
    const formattedInteger = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ".");

    const formattedNumber = includeDecimals
      ? `${formattedInteger},${decimal}`
      : formattedInteger;

    return isNegative ? `- ${formattedNumber}` : formattedNumber;
  };

  if (Math.abs(value) >= 1e12) {
    // Trilioni
    return formatNumber(value / 1e12) + " T";
  } else if (Math.abs(value) >= 1e9) {
    // Miliardi
    return formatNumber(value / 1e9, false) + " B";
  } else if (Math.abs(value) >= 1e6) {
    // Milioni (senza decimali)
    return formatNumber(value / 1e6, false) + " M";
  } else if (Math.abs(value) >= 1e3) {
    // Migliaia (senza decimali)
    return formatNumber(value / 1e3, false) + " k";
  }

  return value.toString(); // Valori più piccoli restituiti direttamente
}

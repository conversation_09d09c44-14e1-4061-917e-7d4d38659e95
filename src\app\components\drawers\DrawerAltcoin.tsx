import { ICoinmarketcapCoinsData } from "@/app/interfaces/coinmarketcap";
import {
  <PERSON>,
  CardBody,
  Drawer,
  DrawerB<PERSON>,
  Drawer<PERSON>ontent,
  DrawerFooter,
  DrawerHeader,
  Progress,
} from "@heroui/react";
import { memo, useEffect, useRef } from "react";

export default function DrawerAltcoin({
  coinmarketcapCoinsData,
  isDrawerOpen,
  onClose,
}: {
  coinmarketcapCoinsData: ICoinmarketcapCoinsData[];
  isDrawerOpen: boolean;
  onClose: () => void;
}) {
  const btcData: { [key: string]: number } = {
    btc7d: coinmarketcapCoinsData?.find((item) => item.slug === "bitcoin")
      ?.quote.USD.percent_change_7d!,
    btc30d: coinmarketcapCoinsData?.find((item) => item.slug === "bitcoin")
      ?.quote.USD.percent_change_30d!,
    btc90d: coinmarketcapCoinsData?.find((item) => item.slug === "bitcoin")
      ?.quote.USD.percent_change_90d!,
  };

  const altcoinIndexData: { [key: string]: number } = {
    "7gg":
      (coinmarketcapCoinsData
        .sort((a, b) => a.cmc_rank - b.cmc_rank)
        .slice(1, 50)
        .filter((item) => item.quote.USD.percent_change_7d! > btcData.btc7d)
        .length /
        coinmarketcapCoinsData.slice(1, 50).length) *
      100,
    "30gg":
      (coinmarketcapCoinsData
        .sort((a, b) => a.cmc_rank - b.cmc_rank)
        .slice(1, 50)
        .filter((item) => item.quote.USD.percent_change_30d! > btcData.btc30d)
        .length /
        coinmarketcapCoinsData.slice(1, 50).length) *
      100,
    "90gg":
      (coinmarketcapCoinsData
        .sort((a, b) => a.cmc_rank - b.cmc_rank)
        .slice(1, 50)
        .filter((item) => item.quote.USD.percent_change_90d! > btcData.btc90d)
        .length /
        coinmarketcapCoinsData.slice(1, 50).length) *
      100,
  };

  return (
    <>
      <Drawer
        isOpen={isDrawerOpen}
        onOpenChange={onClose}
        placement="bottom"
        size="full"
      >
        <DrawerContent>
          {(onClose) => (
            <>
              <DrawerHeader className="flex justify-center">
                <div className="w-100 flex flex-col items-center">
                  <div className="text-md">Altcoin Season Index</div>
                  <div className="text-xs text-zinc-500">
                    (Performance delle TOP 50 vs BTC)
                  </div>
                </div>
              </DrawerHeader>
              <DrawerBody className=" p-2">
                <div className="grid grid-cols-3 gap-3">
                  {["7gg", "30gg", "90gg"].map((item: string, index) => (
                    <Card
                      classNames={{
                        base: "rounded-xl",
                      }}
                      key={index}
                    >
                      <CardBody>
                        <div className="flex flex-col justify-between gap-1 w-100 text-center">
                          <div className="flex flex-col item-center gap-1">
                            <span className="text-xs text-zinc-400">
                              {item}
                            </span>
                            <span className="text-md font-bold">
                              {Math.ceil(altcoinIndexData[item])}{" "}
                              <span className="text-zinc-400  text-sm font-normal">
                                / 100
                              </span>
                            </span>
                          </div>
                          <div className="relative w-100">
                            <Progress
                              value={altcoinIndexData[item]}
                              size="sm"
                              classNames={{
                                base: "w-100 mt-2 rounded-full relative rounded-full border border-gray-700 bg-gradient-to-r from-orange-500 to-blue-600", // Bordi arrotondati per la barra completa
                                track: "bg-transparent", // Sfondo scuro per il track
                                indicator: "bg-transparent", // Gradiente sull'indicatore
                                label:
                                  "tracking-wider font-medium text-gray-600",
                                value: "text-gray-400",
                              }}
                            ></Progress>
                            {/* Mark sul punto esatto */}
                            <div
                              className="absolute w-3 h-3 rounded-full bg-white border-2 border-gray-700 shadow-md "
                              style={{
                                left: `calc(${altcoinIndexData[item]}% - 6px)`, // Posizionamento dinamico
                                top: 4.5,
                              }}
                            />
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>

                <div className="w-100 flex flex-col items-center font-bold mt-4">
                  <div className="text-md"> Cap. Mercato Altcoin</div>
                  <div className="text-xs text-zinc-500">
                    (Cap. Mercato totale escludendo BTC, ETH, USDT, USDC)
                  </div>
                </div>
                <TradingViewChart></TradingViewChart>
              </DrawerBody>
              <DrawerFooter>
                {/* <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
                <Button color="primary" onPress={onClose}>
                  Action
                </Button> */}
              </DrawerFooter>
            </>
          )}
        </DrawerContent>
      </Drawer>
    </>
  );
}

const TradingViewChart = memo(() => {
  const container = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const script = document.createElement("script");
    script.src =
      "https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js";
    script.type = "text/javascript";
    script.async = true;
    script.innerHTML = `
        {
          "autosize": true,
          "symbol": "TOTAL3-USDT-USDC",
          "interval": "D",
          "timezone": "Europe/Rome",
          "theme": "dark",
          "style": "2",
          "locale": "it",
          "hide_legend": true,
          "hide_top_toolbar": true,
          "allow_symbol_change": false,
          "save_image": false,
          "calendar": false,
          "hide_volume": true,
          "support_host": "https://www.tradingview.com"
        }`;
    container!.current!.appendChild(script);
  }, []);

  return (
    <div
      className="tradingview-widget-container"
      ref={container}
      style={{ height: "100%", width: "100%" }}
    >
      <div
        className="tradingview-widget-container__widget"
        style={{ height: "calc(100% - 12px)", width: "100%" }}
      ></div>
      <div className="tradingview-widget-copyright">
        <a rel="noopener nofollow" target="_blank">
          {/* <span className="blue-text">
            Segui tutti i mercati su TradingView
          </span> */}
        </a>
      </div>
    </div>
  );
});

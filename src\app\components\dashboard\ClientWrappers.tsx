"use client";

import { BtcGlobalWidget } from "@/app/components/widgets/BtcGlobalWidget";
import { EtfWidget } from "@/app/components/widgets/EtfWidget";
import { EthGlobalWidget } from "@/app/components/widgets/EthGlobalWidget";
import {
  useBitboTreasuries,
  useCoingeckoCoins,
  useCoingeckoCompanyHolders,
  useGoogleSheetsEtf,
} from "@/app/hooks/useApiWithCache";
import { Skeleton } from "@heroui/react";

// Hook per ETF History ora implementato con cache
function useEtfHistory() {
  return useGoogleSheetsEtf();
}

export function EtfWidgetWrapperClient() {
  const { data: etfHistory, loading, error } = useEtfHistory();

  if (loading) {
    return (
      <div className="grid grid-cols-2 gap-4">
        <Skeleton className="min-h-[200px] rounded-2xl" />
        <Skeleton className="min-h-[200px] rounded-2xl" />
      </div>
    );
  }

  if (error || !etfHistory) {
    return (
      <div className="grid grid-cols-2 gap-4">
        <div className="min-h-[200px] bg-gray-800 rounded-2xl flex items-center justify-center">
          <p className="text-gray-400">ETF BTC non disponibile</p>
        </div>
        <div className="min-h-[200px] bg-gray-800 rounded-2xl flex items-center justify-center">
          <p className="text-gray-400">ETF ETH non disponibile</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <EtfWidget selectedName="btc" etfHistory={etfHistory} />
      <EtfWidget selectedName="eth" etfHistory={etfHistory} />
    </>
  );
}

export function GlobalWidgetWrapperClient() {
  const {
    data: coingeckoCoinsData,
    loading: coingeckoLoading,
    error: coingeckoError,
  } = useCoingeckoCoins();

  const {
    data: coingeckoCompanyHoldersData,
    loading: companyLoading,
    error: companyError,
  } = useCoingeckoCompanyHolders();

  const {
    data: btcGlobal,
    loading: btcLoading,
    error: btcError,
  } = useBitboTreasuries();

  const { data: etfHistory } = useEtfHistory();

  // Mostra loading se uno dei servizi principali sta caricando
  if (coingeckoLoading || companyLoading || btcLoading) {
    return (
      <div className="md:grid md:grid-cols-2 mx-3 my-8 gap-5">
        <div className="mb-8 md:mb-0">
          <Skeleton className="min-h-[300px] rounded-2xl" />
        </div>
        <Skeleton className="min-h-[300px] rounded-2xl" />
      </div>
    );
  }

  // Mostra errore se ci sono problemi critici
  if (coingeckoError || companyError || btcError) {
    return (
      <div className="md:grid md:grid-cols-2 mx-3 my-8 gap-5">
        <div className="mb-8 md:mb-0">
          {btcError ? (
            <div className="min-h-[300px] bg-gray-800 rounded-2xl flex items-center justify-center">
              <div className="text-center">
                <p className="text-red-400 mb-2">⚠️ Errore BTC Global</p>
                <p className="text-xs text-gray-400">{btcError.message}</p>
              </div>
            </div>
          ) : (
            <BtcGlobalWidget btcGlobal={btcGlobal!} />
          )}
        </div>

        {coingeckoError || companyError ? (
          <div className="min-h-[300px] bg-gray-800 rounded-2xl flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-400 mb-2">⚠️ Errore ETH Global</p>
              <p className="text-xs text-gray-400">
                {coingeckoError?.message || companyError?.message}
              </p>
            </div>
          </div>
        ) : (
          <EthGlobalWidget
            etfHistory={etfHistory}
            coingeckoCompanyHoldersData={coingeckoCompanyHoldersData!}
            coingeckoCoinsData={coingeckoCoinsData!}
          />
        )}
      </div>
    );
  }

  // Se non ci sono dati, mostra messaggio
  if (!coingeckoCoinsData || !coingeckoCompanyHoldersData || !btcGlobal) {
    return (
      <div className="md:grid md:grid-cols-2 mx-3 my-8 gap-5">
        <div className="mb-8 md:mb-0">
          <div className="min-h-[300px] bg-gray-800 rounded-2xl flex items-center justify-center">
            <p className="text-gray-400">Dati BTC non disponibili</p>
          </div>
        </div>
        <div className="min-h-[300px] bg-gray-800 rounded-2xl flex items-center justify-center">
          <p className="text-gray-400">Dati ETH non disponibili</p>
        </div>
      </div>
    );
  }

  return (
    <div className="md:grid md:grid-cols-2 mx-3 my-8 gap-5">
      <div className="mb-8 md:mb-0">
        <BtcGlobalWidget btcGlobal={btcGlobal} />
      </div>

      <EthGlobalWidget
        etfHistory={etfHistory}
        coingeckoCompanyHoldersData={coingeckoCompanyHoldersData}
        coingeckoCoinsData={coingeckoCoinsData}
      />
    </div>
  );
}

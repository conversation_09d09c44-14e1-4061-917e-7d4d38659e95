"use client";

import { ICoinmarketcapCoinsData } from "@/app/interfaces/coinmarketcap";
import { getColorForPercentage } from "@/app/utils/getColorForPercentage";
import {
  Progress,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import React, { useMemo, useState } from "react";
import { ICoingeckoCoinsData } from "../../interfaces/coingecko";
import { formatCurrency } from "../../utils/format-numbers/formatCurrency";
import { formatMarketCap } from "../../utils/format-numbers/formatMarketCap";

export default function CryptoList({
  coingeckoCoinsData,
  coinmarketcapCoinsData,
}: {
  coingeckoCoinsData: ICoingeckoCoinsData[];
  coinmarketcapCoinsData: ICoinmarketcapCoinsData[];
}) {
  // useEffect(() => {
  //   console.log("render CryptoList, isloading", isLoading);
  // });

  // console.log("company data", coingeckoCompanyHoldersData);

  let allTags: {
    name: string;
    numberOfTags: number;
    marketCap: number;
    coins: ICoinmarketcapCoinsData[];
  }[] = [];

  coinmarketcapCoinsData.forEach((crypto) =>
    crypto.tags.forEach((tag) => {
      const foundTag = allTags.find((item) => item.name === tag);
      if (foundTag) {
        foundTag.numberOfTags += 1;
        foundTag.marketCap += crypto.quote.USD.market_cap;
        foundTag.coins.push(crypto);
      } else {
        allTags.push({
          name: tag,
          numberOfTags: 1,
          marketCap: crypto.quote.USD.market_cap,
          coins: [crypto],
        });
      }
    })
  );

  // coinmarketcapData?.forEach((crypto) => {
  //   const firstTag = crypto.tags[0];

  //   if (firstTag) {
  //     const foundTag = allTags.find((item) => item.name === firstTag);

  //     if (foundTag) {
  //       foundTag.numberOfTags += 1;
  //       foundTag.marketCap += crypto.quote.USD.market_cap;
  //       foundTag.coins.push(crypto);
  //     } else {
  //       allTags.push({
  //         name: firstTag,
  //         numberOfTags: 1,
  //         marketCap: crypto.quote.USD.market_cap,
  //         coins: [crypto],
  //       });
  //     }
  //   }
  // });

  allTags = allTags
    .filter((tag) => !tag.name.includes("portfolio"))
    .sort((a, b) => b.marketCap - a.marketCap)
    .map((t) => ({
      ...t,
      coins: t.coins.sort((a, b) => a.cmc_rank - b.cmc_rank),
    }));

  // console.log(
  //   "ALL TAGS",
  //   allTags
  //     .filter((t) => t.name.includes("agents"))
  //     .map((t2) => ({
  //       ...t2,
  //       // coins: t2.coins.filter(
  //       //   (c) =>
  //       //     c.tags[0] === t2.name ||
  //       //     c.tags[1] === t2.name ||
  //       //     c.tags[2] === t2.name
  //       // ),
  //     }))
  // );

  let filteredCoingeckoCoinsData = coingeckoCoinsData.filter(
    (crypto) =>
      !crypto.name.toLowerCase().includes("wrapped") &&
      !crypto.name.toLowerCase().includes("stake") &&
      !crypto.symbol.toLowerCase().includes("usd") &&
      !crypto.symbol.toLowerCase().includes("dai") &&
      !crypto.symbol.toLowerCase().includes("weth") &&
      !crypto.symbol.toLowerCase().includes("wbtc") &&
      !crypto.symbol.toLowerCase().includes("etc")
  );

  filteredCoingeckoCoinsData = filteredCoingeckoCoinsData.map(
    (crypto: ICoingeckoCoinsData) => {
      const foundCrypto = coinmarketcapCoinsData.find(
        (item) => item.symbol.toLowerCase() === crypto.symbol.toLowerCase()
      );
      return {
        ...crypto,
        market_cap_dominance:
          foundCrypto?.quote?.USD?.market_cap_dominance || 0,
      };
    }
  );

  // Filtra le criptovalute di CoinMarketCap per escludere quelle con tag "stablecoin" o "fiat"
  // const filteredCoinmarketcapData =
  //   coinmarketcapData?.filter(
  //     (crypto) =>
  //       !crypto.tags.includes("stablecoin") && !crypto.tags.includes("fiat")
  //   ) || [];

  interface CryptoTableProps {
    filteredCoingeckoData: ICoingeckoCoinsData[];
  }

  const CryptoTable: React.FC<CryptoTableProps> = ({
    filteredCoingeckoData,
  }) => {
    const columns = [
      { uid: "position", name: "#" },
      { uid: "image", name: "" },
      { uid: "name", name: "Cripto" },
      { uid: "marketCap", name: "Cap. M." },
      { uid: "dominance", name: "Dominanza" },
      { uid: "price", name: "Prezzo" },
      { uid: "ath", name: "ATH" },
      { uid: "athChange", name: "ATH %" },
      // { uid: "emtpy", name: "empty" },
    ];

    const [rowsPerPage, setRowsPerPage] = useState(20);
    const [page, setPage] = useState(1);
    const pages = Math.ceil(filteredCoingeckoData.length / rowsPerPage);
    const items = useMemo(() => {
      const start = (page - 1) * rowsPerPage;
      const end = start + rowsPerPage;

      return filteredCoingeckoData.slice(start, end);
    }, [page, filteredCoingeckoData, rowsPerPage]);

    // console.log("filteredCoingeckoData", filteredCoingeckoData);
    // console.log("filteredCoinmarketcapData", filteredCoinmarketcapData);

    const renderCell = (
      item: ICoingeckoCoinsData,
      columnKey: keyof ICoingeckoCoinsData | string
    ): React.ReactNode => {
      // Funzione per calcolare il colore basato sulla percentuale

      switch (columnKey) {
        case "position":
          return (
            <div className="text-zinc-500">
              {filteredCoingeckoData.indexOf(item) + 1}
            </div>
          );
        case "image":
          return (
            <img
              src={item.image}
              alt={item.name}
              className="rounded-full h-[26px] w-[26px]"
            ></img>
          );
        case "name":
          return (
            <div>
              <div
                className="w-100"
                style={{
                  color: getColorForPercentage(item.ath_change_percentage),
                }}
              >
                {item.symbol.toUpperCase()}
              </div>
              <div className="text-zinc-500 text-xs truncate">
                {item.name.toLowerCase() === "xrp" ? "Ripple" : item.name}
              </div>
            </div>
          );

        case "marketCap":
          return formatMarketCap(item.market_cap);
        case "dominance":
          return (
            <>
              <Progress
                aria-label="Dominanza"
                size="sm"
                value={+item.market_cap_dominance!.toFixed(1)}
                label={
                  item
                    .market_cap_dominance!.toFixed(1)
                    .toString()
                    .replace(".", ",") + "%"
                }
                classNames={{
                  label: "flex-1",
                }}
                // showValueLabel={true}
              />
            </>
          );
        case "price":
          return formatCurrency(item.current_price);
        case "ath":
          return formatCurrency(item.ath);
        case "athChange":
          return item.ath_change_percentage !== undefined ? (
            <div
              style={{
                color: getColorForPercentage(item.ath_change_percentage),
              }}
            >
              {item.ath_change_percentage.toFixed(1).replace(".", ",")}%
            </div>
          ) : (
            "N/A"
          );
        default:
          return null;
      }
    };

    // const TopContent = memo(() => {
    //   return (
    //     <h2 className="text-xl font-bold text-center p-2 bg-tableHeaderBg sticky left-0 rounded-t-xl w-100">
    //       Top Cripto
    //     </h2>
    //   );
    // });

    // const BottomContent = memo(() => {
    //   return (
    //     <div className="py-2 flex justify-center items-center sticky left-0 ">
    //       <Pagination
    //         showControls
    //         size="sm"
    //         classNames={{
    //           cursor: "bg-foreground text-background",
    //         }}
    //         color="default"
    //         page={page}
    //         total={pages}
    //         variant="light"
    //         onChange={setPage}
    //       />
    //     </div>
    //   );
    // });

    const classNames = useMemo(
      () => ({
        wrapper: [
          // "max-h-[600px]",
          "p-0",
          "border-0",
          "overflow-x-auto",
          "overflow-y-hidden",
          // "bg-black",
        ],
        table: [],
        th: [
          "bg-transparent",
          "text-default-500",
          "border-b",
          "border-divider",
        ],
        td: [
          // changing the rows border radius
          // first
          "group-data-[first=true]/tr:first:before:rounded-none",
          "group-data-[first=true]/tr:last:before:rounded-none",
          // middle
          "group-data-[middle=true]/tr:before:rounded-none",
          // last
          "group-data-[last=true]/tr:first:before:rounded-none",
          "group-data-[last=true]/tr:last:before:rounded-none",
        ],
      }),
      []
    );

    return (
      <div className="border-1 border-zinc-800 rounded-xl p-0 m-3 relative overflow-hidden tablet:[margin-right:-20px]">
        {/* <TopContent></TopContent> */}
        <div className="overflow-x-auto tablet:[width:calc(100%-20px)]">
          <Table
            removeWrapper
            title="Top Cripto"
            aria-label="Tabella delle criptovalute"
            className="text-white w-full table-auto p-0 m-0"
            classNames={classNames}
          >
            <TableHeader columns={columns}>
              {(column) => (
                <TableColumn
                  key={column.uid}
                  align="start"
                  className={`whitespace-nowrap  ${
                    column.uid === "dominance"
                      ? "text-end px-2 min-w-[100px] max-w-[120px] pl-6"
                      : column.uid === "athChange"
                      ? "mr-0 text-end"
                      : column.uid === "position"
                      ? "min-w-[32px] px-2 text-end"
                      : column.uid === "image"
                      ? "min-w-[42px] max-w-[42px] p-2"
                      : column.uid === "name"
                      ? "sticky left-0 bg-tableBg p-2 z-20 min-w-[85px] max-w-[85px] "
                      : "text-end min-w-[100px] max-w-[120px] p-2"
                  } `}
                >
                  {column.name}
                </TableColumn>
              )}
            </TableHeader>

            <TableBody items={items}>
              {(item) => (
                <TableRow key={item.id}>
                  {(columnKey) => (
                    <TableCell
                      className={`border-b border-zinc-800
                       ${
                         columnKey === "dominance"
                           ? "text-end px-2 min-w-[100px] max-w-[120px] pl-6"
                           : columnKey === "athChange"
                           ? "mr-[8px] text-end  min-w-[80px] max-w-[80px]"
                           : columnKey === "position"
                           ? "min-w-[32px] px-2 text-end"
                           : columnKey === "image"
                           ? "min-w-[42px] max-w-[42px] p-2 pr-0"
                           : columnKey === "name"
                           ? "sticky left-0 z-10 bg-tableBg min-w-[85px] max-w-[85px] p-2 pl-2"
                           : "text-end px-2 min-w-[100px] max-w-[120px]"
                       }`}
                      // style={
                      //   columnKey === "name"
                      //     ? {
                      //         boxShadow: "2px 0 2px -1px #e5e7eb",
                      //       }
                      //     : undefined
                      // }
                    >
                      {renderCell(item, columnKey.toString())}
                    </TableCell>
                  )}
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {/* <BottomContent></BottomContent> */}
      </div>
    );
  };

  return (
    <div className="overflow-hidden">
      {/* Colonna CoinGecko */}
      <CryptoTable
        filteredCoingeckoData={filteredCoingeckoCoinsData.slice(0, 20)}
      ></CryptoTable>

      {/* Colonna CoinMarketCap */}
      {/* <div >
        <h2>CoinMarketCap</h2>
        {filteredCoinmarketcapData.length > 0 ? (
          <table>
            <thead>
              <tr>
                <th>Nome</th>
                <th>Prezzo</th>
                <th>Dominanza (%)</th>
                <th>Platform</th>
              </tr>
            </thead>
            <tbody>
              {filteredCoinmarketcapData.map((crypto) => (
                <tr key={crypto.id}>
                  <td>
                    {crypto.name} ({crypto.symbol})
                  </td>
                  <td>${crypto.quote.USD.price.toFixed(2)}</td>
                  <td>
                    {crypto.quote.USD.market_cap_dominance
                      ? crypto.quote.USD.market_cap_dominance.toFixed(2)
                      : "N/A"}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p>Nessun dato disponibile da CoinMarketCap.</p>
        )}
      </div> */}
    </div>
  );
}

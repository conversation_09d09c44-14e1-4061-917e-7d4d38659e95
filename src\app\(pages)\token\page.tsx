import TokenList from "@/app/components/tables/TokenList";
import { fetchCryptorankCoinsData } from "@/app/services/http/cryptorankService";

export default async function Token() {
  const _cryptorankCoinsData = await fetchCryptorankCoinsData();

  return (
    <div className="mb-4">
      <p className="m-3 text-sm text-zinc-500 ">
        I token sono rappresentazioni digitali di{" "}
        <span className="text-sm font-bold text-zinc-300">
          valore o diritti
        </span>{" "}
        emessi su una blockchain. <br /> <br />
        Possono fungere da{" "}
        <span className="text-sm font-bold text-zinc-300">
          monete virtuali, strumenti di governance
        </span>{" "}
        (es. diritto di voto in un progetto), o certificare la{" "}
        <span className="text-sm font-bold text-zinc-300">proprietà</span> di un
        bene.
        <br /> <br />
        Sfruttando la tecnologia blockchain, le transazioni di token sono
        trasparenti, sicure e difficilmente modificabili.
      </p>
      <TokenList cryptorankCoinsData={_cryptorankCoinsData}></TokenList>
    </div>
  );
}

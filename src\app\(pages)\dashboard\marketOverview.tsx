"use client";

import { ICoingeckoCoinsData } from "@/app/interfaces/coingecko";
import { timeFromNow } from "@/app/utils/dates/timeFromNow";
import { Card, CardBody, ScrollShadow } from "@heroui/react";
import Image from "next/image";
import React from "react";
interface ICoin {
  name: string;
  ticker: string;
  symbol: string;
  image: string;
}

export function MarketOverview({
  lastNewsReport,
  coingeckoCoinsData,
}: {
  lastNewsReport: {
    summary: string;
    sentiment: string;
    coin_tickers: string[];
    created_at: string;
  };
  coingeckoCoinsData: ICoingeckoCoinsData[];
}): React.ReactNode {
  const sentimentColor = lastNewsReport.sentiment
    .toLowerCase()
    .includes("positivo")
    ? "text-green-500"
    : lastNewsReport.sentiment.toLowerCase().includes("neutro")
    ? "text-yellow-500"
    : "text-red-500";

  const coins: ICoin[] = [
    ...new Map(
      lastNewsReport.coin_tickers
        .map((coinTicker) => {
          const foundCoin = coingeckoCoinsData.find(
            (coin) => coin.symbol.toLowerCase() === coinTicker.toLowerCase()
          );

          // Se foundCoin non esiste, ritorna null
          if (!foundCoin) return null;

          return {
            name: foundCoin.name,
            ticker: foundCoin.symbol,
            symbol: foundCoin.symbol,
            image: foundCoin.image,
          } as ICoin;
        })
        .filter((coin) => coin !== null) // Rimuove eventuali `null`
        .map((coin) => [coin!.ticker, coin]) // Crea coppie [ticker, coin] per Map()
    ).values(), // Estrai solo i valori univoci
  ];

  return (
    <Card
      isBlurred
      // className="border-none bg-background/60 dark:bgtail-default-100/20 "
      // className="border-none bg-blue-800/20"
      shadow="sm"
      style={{
        background: "rgb(23, 29, 39)",
      }}
    >
      <CardBody>
        <p className="text-xs text-zinc-400">
          {timeFromNow(lastNewsReport.created_at, true)}
        </p>
        <div className="flex justify-between items-center mb-3">
          <span>
            {" "}
            SENTIMENT:{" "}
            <span className={sentimentColor}>{lastNewsReport.sentiment}</span>
          </span>

          {lastNewsReport.coin_tickers &&
            lastNewsReport.coin_tickers.length > 0 && (
              <div className="flex gap-0.3">
                {coins.slice(0, 5).map((coin, index) => (
                  <Image
                    src={coin.image}
                    alt={coin.name}
                    width={22}
                    height={22}
                    key={index}
                    className="rounded-full border border-zinc-800"
                  ></Image>
                ))}
              </div>
            )}
        </div>

        <ScrollShadow className="min-h-[100%] max-h-[250px]">
          <p className="whitespace-pre-line text-zinc-400">
            {/* {lastNewsReport.summary} */}
            {lastNewsReport.summary.split("\n").map((line, index) => (
              <span
                key={index}
                className="before:content-['•'] before:mr-2 before:text-blue-300 list-item"
              >
                {line}
              </span>
            ))}
          </p>
        </ScrollShadow>
      </CardBody>
    </Card>
  );

  // 2 DIV CON SCROLL
  // return (
  //   <div className="overflow-x-auto flex space-x-4">
  //     <Card
  //       isBlurred
  //       // className="border-none bg-background/60 dark:bgtail-default-100/20 "
  //       // className="border-none bg-blue-800/20"
  //       shadow="sm"
  //       style={{
  //         background: "rgb(23, 29, 39)",
  //       }}
  //       className="w-[70%] min-w-[70%] "
  //     >
  //       <CardBody>
  //         <p className="text-xs text-zinc-400">11 feb 2025</p>
  //         <div className="flex justify-between items-center mb-2">
  //           <p>
  //             SENTIMENT:{" "}
  //             <span className={sentimentColor}>{lastNewsReport.sentiment}</span>
  //           </p>
  //           {lastNewsReport.coinTickers &&
  //             lastNewsReport.coinTickers.length > 0 && (
  //               <div className="flex gap-0.5">
  //                 {coins.map((coin, index) => (
  //                   <Image
  //                     src={coin.image}
  //                     alt={coin.name}
  //                     width={20}
  //                     height={20}
  //                     key={index}
  //                     className="rounded-full"
  //                   ></Image>
  //                 ))}
  //               </div>
  //             )}
  //         </div>

  //         <ScrollShadow className="min-h-[100%] max-h-[250px]">
  //           <p className="whitespace-pre-line text-zinc-400">
  //             {lastNewsReport.summary}
  //           </p>
  //         </ScrollShadow>
  //       </CardBody>
  //     </Card>
  //     <Card
  //       isBlurred
  //       // className="border-none bg-background/60 dark:bgtail-default-100/20 "
  //       // className="border-none bg-blue-800/20"
  //       shadow="sm"
  //       style={{
  //         background: "rgb(23, 29, 39)",
  //       }}
  //       className="w-[70%] min-w-[70%]"
  //     >
  //       <CardBody>
  //         <p className="text-xs text-zinc-400">Ultimi 7 giorni</p>
  //         <div className="flex justify-between items-center mb-2">
  //           <p>
  //             SENTIMENT:{" "}
  //             <span className={sentimentColor}>{lastNewsReport.sentiment}</span>
  //           </p>
  //           {lastNewsReport.coinTickers &&
  //             lastNewsReport.coinTickers.length > 0 && (
  //               <div className="flex gap-0.5">
  //                 {coins.map((coin, index) => (
  //                   <Image
  //                     src={coin.image}
  //                     alt={coin.name}
  //                     width={20}
  //                     height={20}
  //                     key={index}
  //                     className="rounded-full"
  //                   ></Image>
  //                 ))}
  //               </div>
  //             )}
  //         </div>

  //         <ScrollShadow className="min-h-[100%] max-h-[250px]">
  //           <p className="whitespace-pre-line text-zinc-400">
  //             {lastNewsReport.summary}
  //           </p>
  //         </ScrollShadow>
  //       </CardBody>
  //     </Card>
  //   </div>
  // );
}

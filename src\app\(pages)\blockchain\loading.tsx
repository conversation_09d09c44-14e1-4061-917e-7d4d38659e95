"use client";
import { Skeleton } from "@heroui/react";

export default function Loading() {
  return (
    <>
      <p className="m-3 text-sm text-zinc-500">
        Il Total Value Locked ({" "}
        <span className="text-sm font-bold text-zinc-300">TVL</span> ) è la
        quantità complessiva di fondi che gli utenti depositano e “bloccano”
        all’interno di una piattaforma di finanza decentralizzata ({" "}
        <span className="text-sm font-bold text-zinc-300">DeFi</span> ).
        <br /> <br />
        Il TVL serve quindi come indicatore della popolarità e della fiducia che
        gli utenti ripongono in un determinato progetto DeFi : più alto è il
        TVL, maggiore è il volume di asset in gioco e, in genere, la{" "}
        <span className="text-sm font-bold text-zinc-300">solidità</span>{" "}
        percepita della piattaforma.
      </p>
      <div className="border-1 border-zinc-800 rounded-2xl p-0 m-2 relative overflow-hidden tablet:[margin-right:-20px] min-h-[402px]">
        {/* <h2 className="text-xl font-bold text-center p-2 bg-tableHeaderBg sticky left-0 rounded-t-2xl w-100">
            Top Blockchain
          </h2> */}
        <Skeleton className="min-h-[1000px]"></Skeleton>
      </div>
    </>
  );
}

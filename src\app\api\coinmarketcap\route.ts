import { NextResponse } from 'next/server';

const COINMARKETCAP_URL =
  "https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest?aux=num_market_pairs,cmc_rank,date_added,tags,platform,max_supply,circulating_supply,total_supply,market_cap_by_total_supply,volume_24h_reported,volume_7d,volume_7d_reported,volume_30d,volume_30d_reported,is_market_cap_included_in_calc&limit=500";

export async function GET() {
  try {
    console.log("Effettuando una nuova chiamata a CoinMarketCap - Coins...");
    
    const response = await fetch(COINMARKETCAP_URL, {
      headers: {
        "X-CMC_PRO_API_KEY": process.env.COINMARKETCAP_API_KEY as string,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    const data = responseData.data;

    return NextResponse.json(data);
  } catch (error) {
    console.error("Errore durante il fetch da CoinMarketCap:", error);
    return NextResponse.json(
      { error: "Errore durante il recupero dei dati CoinMarketCap" },
      { status: 500 }
    );
  }
}

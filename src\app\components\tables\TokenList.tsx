"use client";
import { ICryptoRankCoinsData } from "@/app/interfaces/cryptorank";
import { formatCurrency } from "@/app/utils/format-numbers/formatCurrency";
import { getColorForPercentage } from "@/app/utils/getColorForPercentage";
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { useMemo, useState } from "react";
import { formatMarketCap } from "../../utils/format-numbers/formatMarketCap";

export default function TokenList({
  cryptorankCoinsData,
}: {
  cryptorankCoinsData: ICryptoRankCoinsData[];
}): React.ReactNode {
  const filteredCryptorankData: ICryptoRankCoinsData[] =
    cryptorankCoinsData.filter(
      (item: ICryptoRankCoinsData) =>
        item.type === "token" &&
        item.categoryId &&
        item.categoryId !== 37 &&
        item.categoryId !== 65 &&
        +item.marketCap! > 100000000
    );

  // console.log("filtered cryptorank", filteredCryptorankData);

  interface TokensTableProps {
    filteredTokens: ICryptoRankCoinsData[];
  }

  const BlokchainTvlTabel: React.FC<TokensTableProps> = ({
    filteredTokens,
  }) => {
    const columns = [
      { uid: "position", name: "#" },
      { uid: "image", name: "" },
      { uid: "name", name: "Token" },
      { uid: "marketCap", name: "Cap. M." },
      { uid: "category", name: "Categoria" },
      { uid: "rank", name: "Rank" },
      { uid: "price", name: "Prezzo" },
      { uid: "ath", name: "ATH" },
      { uid: "athChange", name: "ATH %" },
    ];

    const [rowsPerPage, setRowsPerPage] = useState(20);
    const [page, setPage] = useState(1);
    const pages = Math.ceil(filteredTokens.length / rowsPerPage);
    const items = useMemo(() => {
      const start = (page - 1) * rowsPerPage;
      const end = start + rowsPerPage;

      return filteredTokens.slice(start, end);
    }, [page, filteredTokens, rowsPerPage]);

    // console.log("filteredTokens", filteredTokens);

    const renderCell = (
      item: ICryptoRankCoinsData,
      columnKey: keyof any | string
    ): React.ReactNode => {
      switch (columnKey) {
        case "position":
          return (
            <div className="text-zinc-500">
              {filteredTokens.indexOf(item) + 1}
            </div>
          );
        case "image":
          return (
            <img
              src={item.images!.native}
              alt={item.name}
              className="rounded-full h-[26px] w-[26px]"
            ></img>
          );
        case "name":
          return (
            <div>
              <div
                className="w-100"
                style={{
                  color: getColorForPercentage(item.ath!.percentChange),
                }}
              >
                {item.symbol!.toUpperCase()}
              </div>
              <div className="text-zinc-500 text-xs truncate">{item.name}</div>
            </div>
          );
        case "marketCap":
          return formatMarketCap(+item.marketCap!);
        case "rank":
          return item.rank!;
        case "category":
          return (
            <div className="text-sm truncate">
              {item.categoryId === 44
                ? "Infrastruttura"
                : item.categoryId === 4
                ? "Servizi"
                : item.category!.name!}
            </div>
          );
        case "price":
          return formatCurrency(+item.price!);
        case "ath":
          return formatCurrency(+item.ath?.value);
        case "athChange":
          return item.ath!.percentChange !== undefined ? (
            <div
              style={{
                color: getColorForPercentage(+item.ath!.percentChange),
              }}
            >
              {(+item.ath!.percentChange).toFixed(1).replace(".", ",")}%
            </div>
          ) : (
            "N/A"
          );
        default:
          return null;
      }
    };

    // const TopContent = memo(() => {
    //   return (
    //     <h2 className="text-xl font-bold text-center p-2 bg-tableHeaderBg sticky left-0 rounded-t-xl w-100">
    //       Top Tokens
    //     </h2>
    //   );
    // });

    // const BottomContent = memo(() => {
    //   return (
    //     <div className="py-2  flex justify-center items-center sticky left-0">
    //       <Pagination
    //         showControls
    //         size="sm"
    //         classNames={{
    //           cursor: "bg-foreground text-background",
    //         }}
    //         color="default"
    //         page={page}
    //         total={pages}
    //         variant="light"
    //         onChange={setPage}
    //       />
    //     </div>
    //   );
    // });

    const classNames = useMemo(
      () => ({
        wrapper: [
          // "max-h-[600px]",
          "p-0",
          "border-0",
          "overflow-x-auto",
          "overflow-y-hidden",
          // "bg-black",
        ],
        table: [],
        th: [
          "bg-transparent",
          "text-default-500",
          "border-b",
          "border-divider",
        ],
        td: [
          // changing the rows border radius
          // first
          "group-data-[first=true]/tr:first:before:rounded-none",
          "group-data-[first=true]/tr:last:before:rounded-none",
          // middle
          "group-data-[middle=true]/tr:before:rounded-none",
          // last
          "group-data-[last=true]/tr:first:before:rounded-none",
          "group-data-[last=true]/tr:last:before:rounded-none",
        ],
      }),
      []
    );

    return (
      <div className="border-1 border-zinc-800 rounded-2xl p-0 m-2 relative overflow-hidden tablet:[margin-right:-20px]">
        {/* <TopContent></TopContent> */}
        <div className="overflow-x-auto tablet:[width:calc(100%-20px)]">
          <Table
            removeWrapper
            title="Top Blockchain"
            aria-label="Tabella delle blockchain"
            className="text-white w-full table-auto p-0 m-0"
            classNames={classNames}
          >
            <TableHeader columns={columns}>
              {(column) => (
                <TableColumn
                  key={column.uid}
                  align="start"
                  className={` whitespace-nowrap  ${
                    column.uid === "category"
                      ? "mr-0 text-end"
                      : column.uid === "rank"
                      ? "mr-[8px]  text-end"
                      : column.uid === "marketCap"
                      ? "mr-0 text-end pl-6"
                      : column.uid === "position"
                      ? "min-w-[32px] px-2 text-end"
                      : column.uid === "image"
                      ? "min-w-[42px] max-w-[42px] p-2"
                      : column.uid === "name"
                      ? "sticky left-0 bg-tableBg p-2 z-10 min-w-[90px] max-w-[90px]"
                      : "text-end min-w-[100px] max-w-[120px] p-2"
                  } `}
                >
                  {column.name}
                </TableColumn>
              )}
            </TableHeader>
            <TableBody items={items}>
              {(item: ICryptoRankCoinsData) => (
                <TableRow key={item.name}>
                  {(columnKey) => (
                    <TableCell
                      className={`border-b border-zinc-800
                     ${
                       columnKey === "category"
                         ? "mr-0 text-end "
                         : columnKey === "rank"
                         ? "mr-[8px] text-end min-w-[80px] max-w-[80px]"
                         : columnKey === "marketCap"
                         ? "text-end pl-6 min-w-[85px] max-w-[85px]"
                         : columnKey === "position"
                         ? "min-w-[32px] px-2 text-end"
                         : columnKey === "image"
                         ? "min-w-[42px] max-w-[42px] p-2 pr-0"
                         : columnKey === "name"
                         ? "sticky left-0 z-10 bg-tableBg min-w-[90px] max-w-[90px] p-2 pl-2"
                         : "text-end px-2 min-w-[100px] max-w-[120px]"
                     }`}
                      // style={
                      //   columnKey === "name"
                      //     ? {
                      //         boxShadow: "2px 0 2px -1px #e5e7eb",
                      //       }
                      //     : undefined
                      // }
                    >
                      {renderCell(item, columnKey.toString())}
                    </TableCell>
                  )}
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {/* <BottomContent></BottomContent> */}
      </div>
    );
  };

  return (
    <div className="overflow-hidden">
      <BlokchainTvlTabel
        filteredTokens={filteredCryptorankData!.slice(0, 20)}
      ></BlokchainTvlTabel>
    </div>
  );
}

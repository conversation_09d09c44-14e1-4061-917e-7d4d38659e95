import { Analytics } from "@vercel/analytics/react";
import type { Metadata } from "next";
import { Roboto } from "next/font/google";
import Footer from "./components/shared/Footer";
import Header from "./components/shared/Header";
import TabsMenu from "./components/shared/Tabs";
import { CacheManager } from "./components/utils/CacheManager";
import "./globals.css";
import { Providers } from "./providers";

// const geistSans = Geist({
//   variable: "--font-geist-sans",
//   subsets: ["latin"],
// });

// const geistMono = Geist_Mono({
//   variable: "--font-geist-mono",
//   subsets: ["latin"],
// });

const roboto = Roboto({
  variable: "--font-roboto",
  subsets: ["latin"],
  weight: ["400", "500", "700"],
});

export const metadata: Metadata = {
  title: "Elite Cripto",
  description: "Dashboard cripto",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const currentTime = new Date();

  return (
    <html lang="en" className="dark">
      <Analytics />
      <body
        className={`${roboto.variable} antialiased md:max-w-[700px] md:mx-auto md:bg-black`}
      >
        <Providers>
          <div className="sticky top-0 z-50 ">
            <Header></Header>
            <TabsMenu></TabsMenu>
          </div>
          <div className="mt-[-5px]">{children}</div>
          <CacheManager />
          {/* {process.env.NODE_ENV === "development" && <CacheTest />} */}
        </Providers>
        <Footer time={currentTime}></Footer>
      </body>
    </html>
  );
}

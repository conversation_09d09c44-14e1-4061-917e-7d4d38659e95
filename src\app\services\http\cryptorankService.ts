import {
  ICryptorankCategories,
  ICryptoRankCoinsData,
} from "@/app/interfaces/cryptorank";

const CRYPTORANK_COINS_URL =
  "https://api.cryptorank.io/v2/currencies/?limit=500&sortBy=rank";
const CRYPTORANK_CATEGORIES_URL =
  "https://api.cryptorank.io/v2/currencies/categories";

// Fetch coins
export async function fetchCryptorankCoinsData(): Promise<
  ICryptoRankCoinsData[]
> {
  console.log("Effettuando una nuova chiamata a Cryptorank - Coins...");
  try {
    const response = await fetch(CRYPTORANK_COINS_URL, {
      headers: {
        "X-Api-Key": process.env.CRYPTORANK_API_KEY as string,
      },
      // next: { revalidate: 60 * 60 },
    }); // Aggiorna la cache ogni 1 ora

    if (!response.ok) {
      throw new Error("Errore durante il fetch da Cryptorank (Coins)");
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const dataResponse: any = await response.json();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const data: any[] = dataResponse.data;

    const response2: ICryptorankCategories[] =
      await fetchCryptorankCategories();

    const dataWithCategories = data.map((item: ICryptoRankCoinsData) => ({
      ...item,
      category: response2?.find((category) => category.id === item.categoryId),
    }));

    return dataWithCategories;
  } catch (error) {
    console.error(
      "Errore durante il fetch o la validazione Cryptorank (Coins)",
      error
    );
    throw new Error("Errore durante il recupero dei dati Cryptorank (Coins)");
  }
}

// Fetch categories
export async function fetchCryptorankCategories(): Promise<
  ICryptorankCategories[]
> {
  console.log("Effettuando una nuova chiamata a Cryptorank - Caategorie...");
  try {
    const response = await fetch(CRYPTORANK_CATEGORIES_URL, {
      headers: {
        "X-Api-Key": process.env.CRYPTORANK_API_KEY as string,
      },
      // next: { revalidate: 60 * 60 }, // Aggiorna la cache ogni 1 ora
    });
    if (!response.ok) {
      throw new Error("Errore durante il fetch da Cryptorank (Categorie)");
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const dataResponse: any = await response.json();
    const data: ICryptorankCategories[] = dataResponse.data;
    return data;
  } catch (error) {
    console.error(
      "Errore durante il fetch o la validazione Cryptorank (Categorie)",
      error
    );
    throw new Error(
      "Errore durante il recupero dei dati Cryptorank (Categorie)"
    );
  }
}

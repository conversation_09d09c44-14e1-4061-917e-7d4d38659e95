// supabaseService.ts

import { IDatabase } from "@/app/interfaces/db/db";
import { INews } from "@/app/interfaces/db/news";
import { createClient } from "@supabase/supabase-js";

// Configurazione del client Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseRoleKey = process.env.SUPABASE_ROLE_KEY!;
const supabase = createClient<IDatabase>(supabaseUrl, supabaseRoleKey);

/**
 * Recupera gli articoli dal database Supabase in un intervallo temporale specifico.
 *
 * @param startTime - Timestamp ISO8601 di inizio
 * @param endTime - Timestamp ISO8601 di fine
 * @returns Un array di articoli
 */
export async function fetchNews(
  start_time: string,
  end_time: string
): Promise<INews[]> {
  const { data: news, error } = await supabase
    .from("news")
    .select("*")
    .gte("created_at", start_time)
    .lte("created_at", end_time);

  if (error) {
    throw new Error(
      `Errore durante il recupero degli articoli: ${error.message}`
    );
  }

  return news || [];
}

export async function addNews(news: INews[]): Promise<void> {
  // Recupera tutte le notizie esistenti dal database
  const { data: existingNews, error: fetchError } = await supabase
    .from("news")
    .select("_id");

  if (fetchError) {
    throw new Error(
      `Errore durante il recupero delle notizie esistenti: ${fetchError.message}`
    );
  }

  // Filtra le notizie da aggiungere escludendo quelle con ID già presenti
  const newNews = news.filter(
    (n) => !existingNews?.some((en) => en._id === n._id)
  );

  if (newNews.length === 0) {
    return;
  }

  // Aggiungi solo le nuove notizie
  const { data, error } = await supabase
    .from("news")
    .upsert(newNews, { onConflict: "_id" });

  if (error) {
    throw new Error(
      `Errore durante l'inserimento delle notizie: ${error.message}`
    );
  }
}

export async function getLatestGeminiSummary(): Promise<{
  summary: string;
  sentiment: string;
  coin_tickers: string[];
  created_at: string;
} | null> {
  const { data, error } = await supabase
    .from("gemini_summaries")
    .select("*")
    .order("created_at", { ascending: false }) // Prendi il più recente
    .limit(1)
    .single();

  if (error) {
    console.error("Errore nel recupero del riassunto da Supabase:", error);
    return null;
  }

  return data;
}

export async function saveGeminiSummary(
  summary: string,
  sentiment: string,
  coin_tickers: string[]
) {
  const { error } = await supabase.from("gemini_summaries").insert([
    {
      summary,
      sentiment,
      coin_tickers: coin_tickers,
      created_at: new Date().toISOString(),
    },
  ]);

  if (error) {
    console.error("Errore nel salvataggio del riassunto su Supabase:", error);
    throw new Error("Errore nel salvataggio del riassunto.");
  }
}

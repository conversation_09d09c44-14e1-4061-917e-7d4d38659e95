"use client";

import {
  But<PERSON>,
  Dropdown,
  Dropdown<PERSON><PERSON>,
  DropdownMenu,
  DropdownTrigger,
  Modal,
  ModalBody,
  ModalContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>dalHeader,
  <PERSON>vbar,
  <PERSON>v<PERSON><PERSON>rand,
  NavbarContent,
} from "@heroui/react";

import { signOut } from "next-auth/react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import React, { useState } from "react";

export default function Header() {
  const pathname = usePathname();
  const router = useRouter();
  // Nascondi il menu se siamo su /login
  if (pathname === "/login") {
    return null;
  }

  return (
    <Navbar
      isBordered
      maxWidth="full"
      position="static"
      isBlurred={false}
      classNames={{
        wrapper: "px-3 md:bg-black",
        base: "border-none",
      }}
    >
      <NavbarBrand
        className="w-[30px] cursor-pointer"
        onClick={() => router.push("/")}
      >
        <Image
          src="/logo.png"
          alt="logo"
          width={25}
          height={25}
          className="rounded-2xl "
        />
        <span className="ml-2 text-xl text-white tracking-wider font-semibold">
          Elite Cripto
        </span>
      </NavbarBrand>

      <NavbarContent justify="end">
        <HeaderDropdown />
      </NavbarContent>
    </Navbar>
  );
}

const HeaderDropdown = (): React.ReactNode => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  return (
    <div>
      <LogoutModal
        isModalOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      ></LogoutModal>
      <Dropdown backdrop="opaque" closeOnSelect>
        <DropdownTrigger>
          <button
            type="button"
            aria-label="menu"
            className=" flex justify-center items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="20"
              width="20"
              viewBox="0 0 448 512"
            >
              <path
                className="fill-zinc-400"
                d="M0 96C0 78.3 14.3 64 32 64l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 128C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 288c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32L32 448c-17.7 0-32-14.3-32-32s14.3-32 32-32l384 0c17.7 0 32 14.3 32 32z"
              />
            </svg>
          </button>
        </DropdownTrigger>
        <DropdownMenu aria-label="Static Actions">
          <DropdownItem key="logout" onPress={() => setIsModalOpen(true)}>
            <LogoutButton></LogoutButton>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>
  );
};

const LogoutButton = (): React.ReactNode => {
  return (
    <>
      <button
        type="button"
        aria-label="logout-button"
        className="flex justify-center items-center text-red-500"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
          className="w-[15px] h-[15px] mr-2"
        >
          <path
            className="fill-red-500"
            d="M502.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-128-128c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L402.7 224 192 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l210.7 0-73.4 73.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l128-128zM160 96c17.7 0 32-14.3 32-32s-14.3-32-32-32L96 32C43 32 0 75 0 128L0 384c0 53 43 96 96 96l64 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-64 0c-17.7 0-32-14.3-32-32l0-256c0-17.7 14.3-32 32-32l64 0z"
          />
        </svg>{" "}
        Logout
      </button>
    </>
  );
};

const LogoutModal = ({
  isModalOpen,
  onClose,
}: {
  isModalOpen: boolean;
  onClose: () => void;
}): React.ReactNode => {
  const [isLoading, setIsLoading] = useState(false);
  const handleLogout = () => {
    setIsLoading(true);
    signOut();
  };
  return (
    <>
      <Modal
        isOpen={isModalOpen}
        onClose={onClose}
        placement="center"
        className="mx-6"
        classNames={{
          backdrop: "bg-black/75",
        }}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">Logout</ModalHeader>
              <ModalBody>
                <p>Sei sicuro di voler effettuare il logout?</p>
              </ModalBody>
              <ModalFooter>
                <Button
                  color="primary"
                  variant="light"
                  onPress={onClose}
                  isDisabled={isLoading}
                >
                  Annulla
                </Button>
                <Button
                  color="danger"
                  onPress={handleLogout}
                  isDisabled={isLoading}
                  isLoading={isLoading}
                >
                  {isLoading ? null : "Logout"}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

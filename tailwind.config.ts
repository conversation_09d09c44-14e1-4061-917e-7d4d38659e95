import { heroui } from "@heroui/react";
import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        tableBg: "#0a0a0a",
        tableHeaderBg: "#222236",
        btcColor: "#F7931A",
        // tableHeaderBg: "#090e1a",
        // tableBorderColor: "#1f2937",
      },
      screens: {
        tablet: { max: "768px" },
      },
    },
  },
  darkMode: "class",
  plugins: [heroui()],
} satisfies Config;

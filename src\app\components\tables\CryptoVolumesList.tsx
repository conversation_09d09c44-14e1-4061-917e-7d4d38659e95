"use client";

import {
  Progress,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { useMemo, useState } from "react";
import { ICoingeckoCoinsData } from "../../interfaces/coingecko";
import { ICoinmarketcapCoinsData } from "../../interfaces/coinmarketcap";
import { formatMarketCap } from "../../utils/format-numbers/formatMarketCap";

export default function CryptoVolumesList({
  coingeckoCoinsData,
  coinmarketcapCoinsData,
}: {
  coingeckoCoinsData?: ICoingeckoCoinsData[];
  coinmarketcapCoinsData?: ICoinmarketcapCoinsData[];
}): React.ReactNode {
  // console.log("sortedBlockchainTvl with FEES", filteredBlockchainFees);
  // console.log("coinmarketcapData crypto volumes", coinmarketcapData);
  // console.log("render BlockchainList, isloading", isLoading);

  const filteredCoinmarketcapData = coinmarketcapCoinsData!
    .slice(0)
    .filter(
      (item) =>
        !item.tags.includes("stablecoin") && !item.slug.includes("classic")
    );

  const sortedCoinmarketcapData = filteredCoinmarketcapData!
    .slice(0)
    .sort(
      (a: ICoinmarketcapCoinsData, b: ICoinmarketcapCoinsData) =>
        b.quote.USD.volume_30d! - a.quote.USD.volume_30d!
    );

  interface CryptoVolumesTableProps {
    cryptoVolumes: ICoinmarketcapCoinsData[];
  }

  const BlokchainTvlTabel: React.FC<CryptoVolumesTableProps> = ({
    cryptoVolumes,
  }) => {
    const columns = [
      { uid: "position", name: "#" },
      { uid: "image", name: "" },
      { uid: "name", name: "Cripto" },

      { uid: "30d", name: "30d" },
      { uid: "30dPercent", name: "30d %" },
      { uid: "7d", name: "7g" },
      { uid: "7dPercent", name: "7g %" },
    ];

    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [page, setPage] = useState(1);
    const pages = Math.ceil(cryptoVolumes.length / rowsPerPage);
    const items = useMemo(() => {
      const start = (page - 1) * rowsPerPage;
      const end = start + rowsPerPage;

      return cryptoVolumes.slice(start, end);
    }, [page, cryptoVolumes, rowsPerPage]);

    // console.log("sortedCoinmarketcapData", cryptoVolumes);

    const renderCell = (
      item: ICoinmarketcapCoinsData,
      columnKey: keyof any | string
    ): React.ReactNode => {
      let imageUrl = coingeckoCoinsData!.find(
        (coin: ICoingeckoCoinsData) =>
          coin.symbol.toLowerCase() === item.symbol?.toLowerCase()
      )?.image;

      switch (columnKey) {
        case "position":
          return (
            <div className="text-zinc-500">
              {cryptoVolumes.indexOf(item) + 1}
            </div>
          );
        case "image":
          return (
            <img
              src={imageUrl}
              alt={item.name}
              className="rounded-full h-[26px] w-[26px]"
            ></img>
          );
        case "name":
          return (
            <div className="align-middle">
              <div className="w-100 truncate">{item.symbol?.toUpperCase()}</div>
              <div className=" text-zinc-500 text-xs truncate">
                {item.name.toLowerCase() === "xrp" ? "Ripple" : item.name}
              </div>
            </div>
          );

        case "7d":
          return formatMarketCap(item.quote.USD.volume_7d!);
        case "7dPercent":
          const _7dPercent = (
            (item.quote.USD.volume_7d! /
              cryptoVolumes.reduce(
                (sum: any, c: ICoinmarketcapCoinsData) =>
                  sum + c.quote.USD.volume_7d!,
                0
              )) *
            100
          ).toFixed(1);
          return (
            <>
              <Progress
                aria-label="Dominanza"
                size="sm"
                value={+_7dPercent}
                label={_7dPercent.replace(".", ",") + "%"}
                // showValueLabel={true}
                classNames={{
                  label: "flex-1",
                }}
              />
            </>
          );

        case "30d":
          return formatMarketCap(item.quote.USD.volume_30d!);
        case "30dPercent":
          const _30dPercent = (
            (item.quote.USD.volume_30d! /
              cryptoVolumes.reduce(
                (sum: any, c: ICoinmarketcapCoinsData) =>
                  sum + c.quote.USD.volume_30d!,
                0
              )) *
            100
          ).toFixed(1);
          return (
            <>
              <Progress
                aria-label="Dominanza"
                size="sm"
                value={+_30dPercent}
                label={_30dPercent.replace(".", ",") + "%"}
                classNames={{
                  label: "flex-1",
                }}
              />
            </>
          );
        default:
          return null;
      }
    };

    // const TopContent = memo(() => {
    //   return (
    //     <h2 className="text-xl font-bold text-center p-2 bg-tableHeaderBg sticky left-0 rounded-t-xl w-100">
    //       Top Volumi
    //     </h2>
    //   );
    // });

    // const BottomContent = memo(() => {
    //   return (
    //     <div className="py-2  flex justify-center items-center sticky left-0">
    //       <Pagination
    //         showControls
    //         size="sm"
    //         classNames={{
    //           cursor: "bg-foreground text-background",
    //         }}
    //         color="default"
    //         page={page}
    //         total={pages}
    //         variant="light"
    //         onChange={setPage}
    //       />
    //     </div>
    //   );
    // });

    const classNames = useMemo(
      () => ({
        wrapper: [
          // "max-h-[600px]",
          "p-0",
          "border-0",
          "overflow-x-auto",
          "overflow-y-hidden",
          // "bg-black",
        ],
        table: [],
        th: [
          "bg-transparent",
          "text-default-500",
          "border-b",
          "border-divider",
        ],
        td: [
          // changing the rows border radius
          // first
          "group-data-[first=true]/tr:first:before:rounded-none",
          "group-data-[first=true]/tr:last:before:rounded-none",
          // middle
          "group-data-[middle=true]/tr:before:rounded-none",
          // last
          "group-data-[last=true]/tr:first:before:rounded-none",
          "group-data-[last=true]/tr:last:before:rounded-none",
        ],
      }),
      []
    );

    return (
      <div className="border-1 border-zinc-800 rounded-2xl p-0 m-2 relative overflow-hidden tablet:[margin-right:-20px]">
        {/* <TopContent></TopContent> */}
        <div className="overflow-x-auto tablet:[width:calc(100%-20px)]">
          <Table
            removeWrapper
            title="Top Volumi"
            aria-label="Tabella delle blockchain"
            className="text-white w-full table-auto p-0 m-0"
            classNames={classNames}
          >
            <TableHeader columns={columns}>
              {(column) => (
                <TableColumn
                  key={column.uid}
                  align="start"
                  className={` whitespace-nowrap  ${
                    column.uid === "7dPercent"
                      ? "mr-0 text-end  min-w-[115px] max-w-[115px]"
                      : column.uid === "30dPercent"
                      ? "text-end min-w-[100px] max-w-[100px] pr-6"
                      : column.uid === "position"
                      ? "min-w-[32px] px-2 text-end"
                      : column.uid === "image"
                      ? "min-w-[42px] max-w-[42px] p-2"
                      : column.uid === "name"
                      ? "sticky left-0 bg-tableBg p-2 z-10 min-w-[90px] max-w-[90px] pl-2"
                      : "text-end min-w-[100px] max-w-[120px] p-2"
                  } `}
                >
                  {column.name}
                </TableColumn>
              )}
            </TableHeader>
            <TableBody items={items}>
              {(item: ICoinmarketcapCoinsData) => (
                <TableRow key={item.name}>
                  {(columnKey) => (
                    <TableCell
                      className={`border-b border-zinc-800
                     ${
                       columnKey === "30dPercent"
                         ? "mr-0 text-end px-6 min-w-[110px] max-w-[110px] px-6 border-r border-zinc-800 "
                         : columnKey === "fees30d"
                         ? "mr-[8px] text-end min-w-[100px] max-w-[100px]"
                         : columnKey === "fees7d"
                         ? "text-end min-w-[100px] max-w-[120px]"
                         : columnKey === "7dPercent"
                         ? "mr-0 text-end min-w-[115px] max-w-[115px] pl-6"
                         : columnKey === "position"
                         ? "min-w-[32px] px-2 text-end"
                         : columnKey === "image"
                         ? "min-w-[42px] max-w-[42px] p-2 pr-0"
                         : columnKey === "name"
                         ? "sticky left-0 z-10 bg-tableBg min-w-[90px] max-w-[90px] p-2"
                         : "text-end px-2 min-w-[90px] max-w-[120px]"
                     }`}
                      // style={
                      //   columnKey === "name"
                      //     ? {
                      //         boxShadow: "2px 0 2px -1px #e5e7eb",
                      //       }
                      //     : undefined
                      // }
                    >
                      {renderCell(item, columnKey.toString())}
                    </TableCell>
                  )}
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {/* <BottomContent></BottomContent> */}
      </div>
    );
  };

  return (
    <div className="overflow-hidden">
      <BlokchainTvlTabel
        cryptoVolumes={sortedCoinmarketcapData.slice(0, 10)}
      ></BlokchainTvlTabel>
    </div>
  );
}

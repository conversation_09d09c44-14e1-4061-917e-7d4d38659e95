import BlockchainList from "@/app/components/tables/BlockchainlList";
import { fetchCoingeckoCoinsData } from "@/app/services/http/coingeckoService";
import { fetchBlockchainData } from "@/app/services/http/defillamaService";

export default async function Blockchain() {
  const [_blockchainData, _coingeckoCoinsData] = await Promise.all([
    fetchBlockchainData(),
    fetchCoingeckoCoinsData(),
  ]);

  return (
    <div className="mb-4">
      <p className="m-3 text-sm text-zinc-500">
        {/* La blockchain è un registro distribuito e immutabile composto da blocchi
        di dati collegati tra loro in ordine cronologico e sigillati
        crittograficamente.
        <br /> <br /> */}
        {/* Ogni blocco contiene informazioni (ad esempio transazioni) validate da
        una rete di nodi, che per raggiungere un accordo utilizzano protocolli
        di consenso.
        <br /> <br /> */}
        Il Total Value Locked ({" "}
        <span className="text-sm font-bold text-zinc-300">TVL</span> ) è la
        quantità complessiva di fondi che gli utenti depositano e “bloccano”
        all’interno di una piattaforma di finanza decentralizzata ({" "}
        <span className="text-sm font-bold text-zinc-300">DeFi</span> ).
        <br /> <br />
        Il TVL serve quindi come indicatore della popolarità e della fiducia che
        gli utenti ripongono in un determinato progetto DeFi : più alto è il
        TVL, maggiore è il volume di asset in gioco e, in genere, la{" "}
        <span className="text-sm font-bold text-zinc-300">solidità</span>{" "}
        percepita della piattaforma.
      </p>
      <BlockchainList
        blockchainData={_blockchainData}
        coingeckoCoinsData={_coingeckoCoinsData}
      ></BlockchainList>
    </div>
  );
}
